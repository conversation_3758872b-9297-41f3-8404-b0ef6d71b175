#!/usr/bin/env python3
"""
Train diffusion model using precomputed dataset.

This script replaces the original training pipeline to use the precomputed
diffusion_training_dataset.csv instead of computing moments on-the-fly.
"""

import torch
import yaml
import os
from src.reconstruction.diffusion import DiffusionProcess, Model, DiffusionConfig
from src.reconstruction.diffusion.data_utils import PrecomputedDiffusionDataLoader


def load_precomputed_training_data(
    config, dataset_path="diffusion_training_dataset.csv"
):
    """
    Load training data from precomputed dataset.

    Parameters:
        config: DiffusionConfig object
        dataset_path: Path to the precomputed dataset

    Returns:
        X_norm, y_norm: Normalized training data
    """
    print("Loading precomputed training data...")

    # Initialize precomputed data loader
    data_loader = PrecomputedDiffusionDataLoader(config, dataset_path)

    # Load and prepare data
    X, y = data_loader.prepare_training_data()

    # Normalize data
    X_norm, y_norm = data_loader.normalize_data(X, y)

    print(f"Training data loaded: X={X_norm.shape}, y={y_norm.shape}")
    print(f"Conditioning columns used: {len(data_loader.conditioning_columns)}")

    return X_norm, y_norm


def create_data_loaders(X, y, batch_size, train_split=0.9):
    """Create training and validation data loaders."""
    n_samples = X.shape[0]
    n_train = int(train_split * n_samples)

    # Split data
    X_train, X_val = X[:n_train], X[n_train:]
    y_train, y_val = y[:n_train], y[n_train:]

    # Convert to tensors
    X_train_tensor = torch.from_numpy(X_train).float()
    y_train_tensor = torch.from_numpy(y_train).float()
    X_val_tensor = torch.from_numpy(X_val).float()
    y_val_tensor = torch.from_numpy(y_val).float()

    # Create batches
    n_train_batches = len(X_train) // batch_size
    n_val_batches = len(X_val) // batch_size

    train_batches = []
    for i in range(n_train_batches):
        start_idx = i * batch_size
        end_idx = start_idx + batch_size
        train_batches.append(
            (X_train_tensor[start_idx:end_idx], y_train_tensor[start_idx:end_idx])
        )

    val_batches = []
    for i in range(n_val_batches):
        start_idx = i * batch_size
        end_idx = start_idx + batch_size
        val_batches.append(
            (X_val_tensor[start_idx:end_idx], y_val_tensor[start_idx:end_idx])
        )

    return train_batches, val_batches


def train_diffusion_model(train_batches, val_batches, diffusion_config):
    """Train the diffusion model."""
    print("Initializing diffusion model...")

    # Initialize model
    model_config = diffusion_config.get_model_config()
    model = Model(**model_config)

    # Initialize diffusion process
    diffusion = DiffusionProcess(model, diffusion_config)

    print(
        f"Model initialized with {sum(p.numel() for p in model.parameters())} parameters"
    )

    # Training loop
    print("Starting training...")
    diffusion.train(train_batches, val_batches)

    return diffusion


def main():
    """Main training function."""

    # Load configuration
    config = yaml.safe_load(open("config_diffusion.yaml"))

    # Initialize diffusion configuration
    diffusion_config = DiffusionConfig()

    # Override config values if needed
    if "diffusion" in config:
        for key, value in config["diffusion"].items():
            if hasattr(diffusion_config, key):
                setattr(diffusion_config, key, value)

    # Set seed
    diffusion_config.set_seed()

    # Create directories
    diffusion_config.create_directories()

    print("DIFFUSION MODEL TRAINING WITH PRECOMPUTED DATASET")
    print(f"Configuration: {diffusion_config.state_name}")
    print(
        f"Device: {diffusion_config.device}, Batch size: {diffusion_config.batch_size}, Epochs: {diffusion_config.epochs}"
    )

    # Check if precomputed dataset exists
    dataset_path = "diffusion_training_dataset.csv"
    if not os.path.exists(dataset_path):
        print(f"\nERROR: Precomputed dataset not found: {dataset_path}")
        print("Please run preprocess_diffusion_data.py first to create the dataset.")
        return

    # Load precomputed training data
    X, y = load_precomputed_training_data(diffusion_config, dataset_path)

    # Verify data shapes match expected configuration
    expected_input_dim = diffusion_config.shape_in[0]
    actual_input_dim = X.shape[1]

    print(f"\nData shape verification:")
    print(f"  Expected input dimension: {expected_input_dim}")
    print(f"  Actual input dimension: {actual_input_dim}")

    if actual_input_dim != expected_input_dim:
        print(f"WARNING: Input dimension mismatch!")
        print(f"  You may need to update the configuration or reprocess the data.")
        # Update config to match actual data
        diffusion_config.shape_in = (actual_input_dim,)
        print(f"  Updated config to use {actual_input_dim} input dimensions.")

    # Create data loaders
    train_batches, val_batches = create_data_loaders(
        X, y, int(diffusion_config.batch_size)
    )

    print(f"\nData loaders created:")
    print(f"  Training batches: {len(train_batches)}")
    print(f"  Validation batches: {len(val_batches)}")

    # Train model
    diffusion = train_diffusion_model(train_batches, val_batches, diffusion_config)

    print("\nTraining completed successfully!")
    print(f"Model saved to: {diffusion_config.ckpt_path}")


if __name__ == "__main__":
    main()
