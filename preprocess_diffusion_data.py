#!/usr/bin/env python3
"""
Preprocess CSV files for diffusion model training.

This script implements the three-part preprocessing system:
1. Process individual CSV files to add precomputed moments and conditioning features
2. Combine all processed files into a single shuffled dataset
3. Prepare for training without on-the-fly computation
"""

import os
import pandas as pd
import numpy as np
from pathlib import Path


# Avoid importing torch-dependent modules for preprocessing
class SimpleDiffusionConfig:
    """Simplified config for preprocessing without torch dependencies."""

    def __init__(self):
        self.pt_conditioning_moments = 4
        self.eta_conditioning_moments = 3
        self.phi_conditioning_moments = 3


def calculate_moments(values, n_moments=4, eps=1e-8):
    """Calculate the first n moments of pT distributions."""
    values = np.asarray(values, dtype=np.float64)
    mu = np.mean(values)
    xc = values - mu
    sigma = np.sqrt(np.mean(xc**2) + eps)
    outs = [mu, sigma]
    for i in range(2, n_moments):
        outs += [np.mean(xc / (sigma + eps)) ** i]
    return np.array(outs, dtype=np.float64)


def circ_moments(alpha, k_max=2):
    """Calculate circular moments."""
    a = np.asarray(alpha, dtype=np.float64)
    a = (a + np.pi) % (2 * np.pi) - np.pi  # wrap to [-pi, pi)
    a = a[np.isfinite(a)]
    if a.size == 0:
        return np.zeros(2 * k_max, dtype=np.float64)
    out = []
    for k in range(1, k_max + 1):
        out += [np.mean(np.cos(k * a)), np.mean(np.sin(k * a))]
    return np.array(out, dtype=np.float64)


def convert_to_four_vectors(data):
    """Convert detector-level data to four-vector format."""
    # Extract lepton four-vectors (already in E, px, py, pz format)
    lep1_4vec = data[["p_l_1_E", "p_l_1_x", "p_l_1_y", "p_l_1_z"]].values
    lep2_4vec = data[["p_l_2_E", "p_l_2_x", "p_l_2_y", "p_l_2_z"]].values

    # Missing momentum (assume pz = 0 for missing momentum, calculate E from pT)
    mpx = data["mpx"].values
    mpy = data["mpy"].values
    mpt = np.sqrt(mpx**2 + mpy**2)

    # For missing momentum, we set E = pT and pz = 0 as initial approximation
    missing_4vec = np.column_stack([mpt, mpx, mpy, np.zeros_like(mpx)])

    return lep1_4vec, lep2_4vec, missing_4vec


def compute_conditioning_features_for_file(df, config):
    """Compute conditioning features for an entire file (process)."""
    # Convert to four-vectors
    lep1_4vec, lep2_4vec, missing_4vec = convert_to_four_vectors(df)

    # Calculate pT for leptons
    px1, py1 = lep1_4vec[:, 1], lep1_4vec[:, 2]
    px2, py2 = lep2_4vec[:, 1], lep2_4vec[:, 2]
    lep1_pt = np.hypot(px1, py1)
    lep2_pt = np.hypot(px2, py2)

    # Compute pT moments for the entire file using original function
    lep1_pt_moments = calculate_moments(lep1_pt, config.pt_conditioning_moments)
    lep2_pt_moments = calculate_moments(lep2_pt, config.pt_conditioning_moments)

    # Compute angle features
    pz1, pz2 = lep1_4vec[:, 3], lep2_4vec[:, 3]
    phi1 = np.arctan2(py1, px1)
    phi2 = np.arctan2(py2, px2)
    dphi = np.arctan2(np.sin(phi1 - phi2), np.cos(phi1 - phi2))

    eta1 = 0.5 * np.log(
        (np.sqrt(px1**2 + py1**2 + pz1**2) + pz1)
        / (np.sqrt(px1**2 + py1**2 + pz1**2) - pz1 + 1e-8)
    )
    eta2 = 0.5 * np.log(
        (np.sqrt(px2**2 + py2**2 + pz2**2) + pz2)
        / (np.sqrt(px2**2 + py2**2 + pz2**2) - pz2 + 1e-8)
    )
    deta = eta1 - eta2
    deta = (deta + np.pi) % (2 * np.pi) - np.pi  # wrap to [-pi, pi)

    # Compute circular moments for angles
    eta_features = circ_moments(deta, config.eta_conditioning_moments)
    phi_features = circ_moments(dphi, config.phi_conditioning_moments)

    # Build conditioning feature dictionary
    conditioning_dict = {}

    # Add pT moments with proper naming (original format returns array)
    for i, value in enumerate(lep1_pt_moments):
        conditioning_dict[f"mom_pT_lep1_{i}"] = value

    for i, value in enumerate(lep2_pt_moments):
        conditioning_dict[f"mom_pT_lep2_{i}"] = value

    # Add circular moment features
    for i, val in enumerate(eta_features):
        conditioning_dict[f"eta_moment_{i}"] = val

    for i, val in enumerate(phi_features):
        conditioning_dict[f"phi_moment_{i}"] = val

    return conditioning_dict


def preprocess_csv_file(csv_path, config, moment_feature="pT", extra_conditioners=None):
    """Process a single CSV file to add precomputed moments and conditioning features."""

    # Load CSV
    df = pd.read_csv(csv_path)

    # Infer process name from filename
    process_name = Path(csv_path).stem
    if "_final_truth" in process_name:
        process_name = process_name.replace("_final_truth", "")
    if "_truth_cuts" in process_name:
        process_name = process_name.replace("_truth_cuts", "")
    if "_1M_MG5" in process_name:
        process_name = process_name.replace("_1M_MG5", "")
    if "_1M_MG" in process_name:
        process_name = process_name.replace("_1M_MG", "")

    # Add process column
    df["process"] = process_name

    # Compute conditioning features for the entire file
    conditioning_dict = compute_conditioning_features_for_file(df, config)

    # Add conditioning features as constant columns
    for feature_name, feature_value in conditioning_dict.items():
        df[feature_name] = feature_value

    # Apply extra conditioners if provided
    if extra_conditioners is not None:
        extra_features = extra_conditioners(df)
        for feature_name, feature_series in extra_features.items():
            df[feature_name] = feature_series

    # Generate output filename
    output_path = csv_path.replace(".csv", "_diffusion_input.csv")

    # Save processed file
    df.to_csv(output_path, index=False)

    print(f"Processed {process_name}: {len(df)} rows -> {output_path}")

    return output_path


def process_csv_files(csv_files, config, moment_feature="pT", extra_conditioners=None):
    """
    Process multiple CSV files to add precomputed moments and conditioning features.

    Parameters:
        csv_files: List of CSV file paths
        config: DiffusionConfig object
        moment_feature: Feature to compute moments for (default: 'pT')
        extra_conditioners: Optional callable for additional conditioning features

    Returns:
        List of output file paths
    """
    output_files = []

    print(f"Processing {len(csv_files)} CSV files...")

    for csv_file in csv_files:
        if not os.path.exists(csv_file):
            print(f"Warning: File {csv_file} not found, skipping...")
            continue

        try:
            output_path = preprocess_csv_file(
                csv_file,
                config,
                moment_feature=moment_feature,
                extra_conditioners=extra_conditioners,
            )
            output_files.append(output_path)

        except Exception as e:
            print(f"Error processing {csv_file}: {e}")
            continue

    print(f"Successfully processed {len(output_files)} files")
    return output_files


def combine_and_shuffle_datasets(
    input_files=None,
    output_path="diffusion_training_dataset.csv",
    max_rows=None,
    random_state=42,
):
    """
    Combine all processed CSV files into a single shuffled training dataset.

    Parameters:
        input_files: List of input CSV files (if None, auto-discover *_diffusion_input.csv)
        output_path: Path for the combined dataset
        max_rows: Optional maximum number of rows (with stratified sampling)
        random_state: Random seed for shuffling

    Returns:
        Path to the combined dataset
    """
    print("Combining and shuffling datasets...")

    # Auto-discover files if not provided
    if input_files is None:
        input_files = list(Path(".").glob("**/*_diffusion_input.csv"))
        input_files = [str(f) for f in input_files]

    if not input_files:
        raise ValueError("No *_diffusion_input.csv files found")

    print(f"Found {len(input_files)} files to combine")

    # Load and combine all files
    dataframes = []
    process_counts = {}

    for file_path in input_files:
        try:
            df = pd.read_csv(file_path)
            dataframes.append(df)

            # Count rows per process
            if "process" in df.columns:
                process_name = df["process"].iloc[0]
                process_counts[process_name] = len(df)

        except Exception as e:
            print(f"  Error loading {file_path}: {e}")
            continue

    if not dataframes:
        raise ValueError("No valid dataframes loaded")

    # Combine all dataframes
    combined_df = pd.concat(dataframes, axis=0, ignore_index=True)
    print(f"\nCombined dataset: {len(combined_df)} total rows")

    # Apply stratified downsampling if requested
    if max_rows is not None and len(combined_df) > max_rows:
        print(f"Downsampling to {max_rows} rows with stratified sampling...")

        if "process" in combined_df.columns:
            # Stratified sampling by process
            sampled_dfs = []
            processes = combined_df["process"].unique()
            rows_per_process = max_rows // len(processes)

            for process in processes:
                process_df = combined_df[combined_df["process"] == process]
                if len(process_df) > rows_per_process:
                    process_sample = process_df.sample(
                        n=rows_per_process, random_state=random_state
                    )
                else:
                    process_sample = process_df
                sampled_dfs.append(process_sample)

            combined_df = pd.concat(sampled_dfs, axis=0, ignore_index=True)
        else:
            # Simple random sampling
            combined_df = combined_df.sample(n=max_rows, random_state=random_state)

        print(f"After downsampling: {len(combined_df)} rows")

    # Shuffle the combined dataset
    combined_df = combined_df.sample(frac=1.0, random_state=random_state).reset_index(
        drop=True
    )

    # Save the final dataset
    combined_df.to_csv(output_path, index=False)

    # Print summary report
    print(f"Combined dataset saved: {output_path}")
    print(f"Total rows: {len(combined_df)}, Total columns: {len(combined_df.columns)}")

    if "process" in combined_df.columns:
        final_process_counts = combined_df["process"].value_counts().sort_index()
        print(f"Rows per process: {dict(final_process_counts)}")

    return output_path


def main():
    """Main function to run the preprocessing pipeline."""

    # Initialize configuration
    config = SimpleDiffusionConfig()

    # Define CSV files to process (using the specified data files)
    csv_files = [
        "data/hww_sherpa_1M_MG_final_detector_sim.csv",
        "data/ww_1M_MG_final_detector_sim_cuts.csv",
        "data/tt_1M_MG_final_detector_sim.csv",
    ]

    print("DIFFUSION DATA PREPROCESSING PIPELINE")

    # Step 1: Process individual CSV files
    output_files = process_csv_files(csv_files, config)

    if not output_files:
        print("No files were successfully processed. Exiting.")
        return

    # Step 2: Combine and shuffle datasets
    final_dataset = combine_and_shuffle_datasets(
        input_files=output_files,
        max_rows=None,  # Set to a number if you want to limit dataset size
        random_state=42,
    )

    print(f"Preprocessing complete! Final dataset: {final_dataset}")


if __name__ == "__main__":
    main()
