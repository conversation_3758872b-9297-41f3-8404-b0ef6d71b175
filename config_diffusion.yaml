# Configuration file for diffusion model training and inference

# Data paths (inherit from main config)
data_path: data/hww_1M_MG_final
raw_data_path: data/hww_1M_MG_no_cuts
processed_features_path: data/hww_1M_MG_final_X
processed_targets_path: data/hww_1M_MG_final_y
detector_sim_path: data/hww_sherpa_1M_MG_final_detector_sim
truth_path: data/hww_1M_MG_final_truth

# Coordinated preprocessed datasets (generated by preprocess_diffusion_data.py)
detector_dataset_path: diffusion_training_dataset.csv
truth_dataset_path: diffusion_training_targets.csv

# Diffusion model specific configuration
diffusion:
  # Random seed
  seed: 42
  random_seed: false
  
  # Data configuration
  load_preprocessed_training_data: true
  
  # Training parameters
  epochs: 600
  batch_size: 1024
  unfold_size: 200000
  sample_size: 10000  # unfold_size // 100
  
  # Model checkpointing
  save_int: 100
  save_ckpts: true
  
  # Output paths
  output_path: "./outputs/diffusion_case1/"
  plots_path: "./plots/diffusion_case1/"
  ckpt_path: "./model-state/diffusion_case1/"

  lr: 3e-4
  beta_1: 1e-4
  beta_T: 0.02
  T: 500
  
  # Model architecture
  hidden_dim: 512
  num_layers: 4
  time_dim: 32
  
  # Normalization ranges (in GeV)
  eta_range: 2.5
  phi_range: 3.14
  pT_range: 200
  E_range: 300
  
  # Device configuration
  device: "cuda"  # Will fallback to CPU if CUDA not available
