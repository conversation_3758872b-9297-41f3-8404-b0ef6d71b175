# Configuration file for diffusion model training and inference

# Data paths (inherit from main config)
data_path: data/hww_1M_MG_final
raw_data_path: data/hww_1M_MG_no_cuts
processed_features_path: data/hww_1M_MG_final_X
processed_targets_path: data/hww_1M_MG_final_y
detector_sim_path: data/hww_sherpa_1M_MG_final_detector_sim
truth_path: data/hww_1M_MG_final_truth

# Coordinated preprocessed datasets (generated by preprocess_diffusion_data.py)
detector_dataset_path: diffusion_training_dataset.csv
truth_dataset_path: diffusion_training_targets.csv

# Diffusion model specific configuration
diffusion:
  # Random seed
  seed: 42
  random_seed: false
  
  # Data configuration
  load_preprocessed_training_data: true
  
  # Training parameters
  epochs: 500
  batch_size: 1024
  unfold_size: 200000
  sample_size: 10000  # unfold_size // 100
  
  # Model checkpointing
  save_int: 100
  save_ckpts: true
  
  # Output paths
  output_path: "./outputs/diffusion_case1/"
  plots_path: "./plots/diffusion_case1/"
  ckpt_path: "./model-state/diffusion_case1/"

  lr: 3e-4
  beta_1: 1e-4
  beta_T: 0.02
  T: 500
  
  # Model architecture
  hidden_dim: 256
  num_layers: 4
  time_dim: 32
  
  # Data dimensions
  n_dims: 8  # 4 components for each of 2 neutrinos (E, px, py, pz)
  pt_conditioning_moments: 4
  eta_conditioning_moments: 3
  phi_conditioning_moments: 3
  
  # Normalization ranges (in GeV)
  eta_range: 3
  phi_range: 3.5
  pT_range: 150
  E_range: 250
  
  # Device configuration
  device: "cuda"  # Will fallback to CPU if CUDA not available

# Integration with existing evaluation system
evaluation:
  # Bell test configuration
  enable_bell_tests: true
  
  # Gell-Mann matrix calculation
  enable_gellmann: true
  
  # Mode calculation settings
  mode_calculation:
    method: "kde"  # Options: "histogram", "kde", "gaussian_fit"
    n_samples_for_modes: 100
    
  # Reconstruction quality metrics
  quality_metrics:
    - "mae"
    - "mse" 
    - "rmse"
    
# Compatibility settings
compatibility:
  # Ensure output format matches existing reconstruction methods
  output_format: "lorentz_vectors"  # Options: "arrays", "lorentz_vectors"
  
  # Integration with existing data preprocessing
  use_existing_preprocessor: true
  
  # Evaluation integration
  integrate_with_evaluation: true
