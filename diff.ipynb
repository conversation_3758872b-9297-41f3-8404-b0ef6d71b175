{"cells": [{"cell_type": "code", "execution_count": 1, "id": "3765901d", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": 2, "id": "6b1f6ea3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saved: outputs/diffusion_case1/unfold_diffusion_lepqua_CT14lo.csv\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>p_v_1_E_diffusion</th>\n", "      <th>p_v_1_x_diffusion</th>\n", "      <th>p_v_1_y_diffusion</th>\n", "      <th>p_v_1_z_diffusion</th>\n", "      <th>p_v_2_E_diffusion</th>\n", "      <th>p_v_2_x_diffusion</th>\n", "      <th>p_v_2_y_diffusion</th>\n", "      <th>p_v_2_z_diffusion</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>150.562406</td>\n", "      <td>-16.070055</td>\n", "      <td>20.315597</td>\n", "      <td>55.727094</td>\n", "      <td>-15.972484</td>\n", "      <td>10.615618</td>\n", "      <td>-12.058404</td>\n", "      <td>13.964798</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>417.365909</td>\n", "      <td>-33.724003</td>\n", "      <td>29.548276</td>\n", "      <td>-377.386093</td>\n", "      <td>-65.588169</td>\n", "      <td>25.559191</td>\n", "      <td>8.042905</td>\n", "      <td>-37.997078</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>23.088260</td>\n", "      <td>8.876422</td>\n", "      <td>22.054849</td>\n", "      <td>-7.529527</td>\n", "      <td>-11.183850</td>\n", "      <td>9.482258</td>\n", "      <td>22.174029</td>\n", "      <td>14.633245</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>131.949574</td>\n", "      <td>49.371418</td>\n", "      <td>6.093334</td>\n", "      <td>91.775350</td>\n", "      <td>15.797146</td>\n", "      <td>18.016491</td>\n", "      <td>-1.011750</td>\n", "      <td>47.737285</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>81.553690</td>\n", "      <td>-35.856873</td>\n", "      <td>48.495114</td>\n", "      <td>-16.306752</td>\n", "      <td>-40.878646</td>\n", "      <td>27.920129</td>\n", "      <td>4.861906</td>\n", "      <td>-13.862845</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   p_v_1_E_diffusion  p_v_1_x_diffusion  p_v_1_y_diffusion  p_v_1_z_diffusion  \\\n", "0         150.562406         -16.070055          20.315597          55.727094   \n", "1         417.365909         -33.724003          29.548276        -377.386093   \n", "2          23.088260           8.876422          22.054849          -7.529527   \n", "3         131.949574          49.371418           6.093334          91.775350   \n", "4          81.553690         -35.856873          48.495114         -16.306752   \n", "\n", "   p_v_2_E_diffusion  p_v_2_x_diffusion  p_v_2_y_diffusion  p_v_2_z_diffusion  \n", "0         -15.972484          10.615618         -12.058404          13.964798  \n", "1         -65.588169          25.559191           8.042905         -37.997078  \n", "2         -11.183850           9.482258          22.174029          14.633245  \n", "3          15.797146          18.016491          -1.011750          47.737285  \n", "4         -40.878646          27.920129           4.861906         -13.862845  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["npy_path = \"outputs/diffusion_case1/unfold_diffusion_lepqua_CT14lo.npy\"\n", "csv_path = npy_path.replace(\".npy\", \".csv\")\n", "\n", "# Load .npy file\n", "data = np.load(npy_path)\n", "\n", "# Wrap into DataFrame (adds column names if you like)\n", "n_cols = data.shape[1]\n", "columns = [f\"col_{i}\" for i in range(n_cols)]\n", "df = pd.DataFrame(data, columns=columns)\n", "df.drop(columns=[\"col_0\"], inplace=True)\n", "\n", "# Rename columns\n", "df.columns = [\n", "    \"p_v_1_E_diffusion\",\n", "    \"p_v_1_x_diffusion\",\n", "    \"p_v_1_y_diffusion\",\n", "    \"p_v_1_z_diffusion\",\n", "    \"p_v_2_E_diffusion\",\n", "    \"p_v_2_x_diffusion\",\n", "    \"p_v_2_y_diffusion\",\n", "    \"p_v_2_z_diffusion\",\n", "]\n", "\n", "# Save to CSV\n", "df.to_csv(csv_path, index=False)\n", "\n", "print(f\"Saved: {csv_path}\")\n", "df.head()"]}, {"cell_type": "code", "execution_count": 3, "id": "99d6aa5d", "metadata": {}, "outputs": [], "source": ["# Load truth data\n", "truth_data = pd.read_csv(\"data/hww_sherpa_1M_MG_final_truth_cuts.csv\")"]}, {"cell_type": "code", "execution_count": 4, "id": "fd55a0f5", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot comparison of neutrinos of truth and diffusion\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "plt.hist(truth_data[\"p_v_1_E_truth\"], bins=50, alpha=0.5, label=\"Truth\", range=(-50, 100), density=True)\n", "plt.hist(df[\"p_v_1_E_diffusion\"], bins=50, alpha=0.5, label=\"Diffusion\", range=(-50, 100), density=True)\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 12, "id": "db6de3d6", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.hist(truth_data[\"p_v_1_E_truth\"], bins=50, alpha=0.5, label=\"Truth\", range=(-10, 250), density=True)\n", "plt.hist(df[\"p_v_1_E_diffusion\"], bins=50, alpha=0.5, label=\"Diffusion\", range=(-10, 250), density=True)\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 6, "id": "7c61b61c", "metadata": {}, "outputs": [], "source": ["# Load ww and hww and compare\n", "ww_data = pd.read_csv(\"data/hww_sherpa_1M_MG_final_truth_cuts.csv\")[:200000]\n", "hww_data = pd.read_csv(\"data/hww_1M_MG_final_truth_cuts.csv\")[:200000]"]}, {"cell_type": "code", "execution_count": 7, "id": "6c029600", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot 1d hist of ww/hww\n", "plt.hist(ww_data[\"p_l_1_E_truth\"], bins=50, alpha=0.5, label=\"Sherpa\", range=(-10, 250), density=True)\n", "plt.hist(hww_data[\"p_l_1_E_truth\"], bins=50, alpha=0.5, label=\"MG\", range=(-10, 250), density=True)\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 8, "id": "e7c90036", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAiwAAAGwCAYAAACKOz5MAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8hTgPZAAAACXBIWXMAAA9hAAAPYQGoP6dpAAAt00lEQVR4nO3df1xUdb7H8feAyg8R/IHyw1DAH5mbgoIS5d50I8HrbfM+3Ba9Fsp6bTPtVmQalaLXrvg7NzXZtTVtt8J129oebUsZSbsVZolWmlb+ivwxoLQyigkGc//oOt1ZEWcQmC/D6/l4nEfMme/5zucczHn7Pd9zjsVut9sFAABgMB9PFwAAAHAlBBYAAGA8AgsAADAegQUAABiPwAIAAIxHYAEAAMYjsAAAAOO183QBTaGurk7Hjx9Xp06dZLFYPF0OAABwgd1u15kzZxQZGSkfn4bHULwisBw/flxRUVGeLgMAADTC119/rWuuuabBNl4RWDp16iTp+x0ODg72cDUAAMAVNptNUVFRju/xhnhFYLl4Gig4OJjAAgBAK+PKdA4m3QIAAOMRWAAAgPEILAAAwHheMYcFAIDLqa2t1YULFzxdRpvVvn17+fr6XnU/BBYAgFey2+2yWq06ffq0p0tp8zp37qzw8PCrulcagQUA4JUuhpUePXooMDCQG4t6gN1u17lz51ReXi5JioiIaHRfBBYAgNepra11hJVu3bp5upw2LSAgQJJUXl6uHj16NPr0EJNuAQBe5+KclcDAQA9XAumH38PVzCUisAAAvBangczQFL8HAgsAADAegQUAABiPSbcAgDblya1ftOjnPXhr/ybv02Kx6OWXX9a4ceOavG9TMcICAIBhTp48qenTp6tXr17y8/NTeHi4UlNT9d5773m6NI9hhAUAAMOMHz9eNTU12rRpk2JjY1VWVqbCwkJVVFQ022fW1NSoQ4cOzdb/1WKEBQAAg5w+fVp///vftWTJEo0aNUq9e/fW8OHDlZ2drZ/+9KeOdqdOndK///u/KzAwUP369dOrr77q1M+ePXs0ZswYBQUFKSwsTHfddZdOnTrleH/kyJGaOXOmHnjgAYWGhio1NVXS96eb1q1bpzFjxiggIECxsbH64x//6NT3nDlz1L9/fwUGBio2NlZz585t9scfMMICwKOaaj5Bc8wTADwhKChIQUFBeuWVV3TDDTfIz8+v3nYLFizQ0qVLtWzZMq1evVqTJk3SV199pa5du+r06dP6yU9+ov/8z//Uk08+qW+//VZz5szRz3/+c7399tuOPjZt2qTp06dfcqpp7ty5Wrx4sX71q1/pd7/7nSZMmKBPP/1U1113nSSpU6dO2rhxoyIjI/Xpp59q2rRp6tSpk2bPnt1sx8Vit9vtzdZ7C7HZbAoJCVFlZaWCg4M9XQ4ANxBY0BzOnz+vw4cPKyYmRv7+/k7vtYZJty+99JKmTZumb7/9VkOHDtXNN9+sCRMmaPDgwZK+HwV5/PHHtXDhQklSVVWVgoKC9Ne//lVpaWl64okn9Pe//11vvPGGo8+jR48qKipKn3/+ufr376+RI0fKZrOppKTE6bMtFovuuecerVu3zrHuhhtu0NChQ/X000/XW+/y5cuVn5+vjz76qN73L/f7cOf7m1NCAAAYZvz48Tp+/LheffVVpaWlqaioSEOHDtXGjRsdbS6GF0nq2LGjgoODHc/s+fjjj7Vt2zbHaE1QUJAGDBggSTp48KBju4SEhHo/Pzk5+ZLX+/btc7zevHmzbrrpJoWHhysoKEiPP/64SktLr3q/G0JgAQDAQP7+/rr11ls1d+5cvf/++5oyZYpycnIc77dv396pvcViUV1dnSTp7Nmzuu2227R7926n5csvv9S//Mu/OLbp2LGj23UVFxdr0qRJ+td//Ve99tpr2rVrlx577DHV1NQ0ck9dwxwWAABagYEDB+qVV15xqe3QoUP10ksvKTo6Wu3auf9Vv337dmVkZDi9HjJkiCTp/fffV+/evfXYY4853v/qq6/c/gx3McICAIBBKioq9JOf/ES///3v9cknn+jw4cPasmWLli5dqttvv92lPmbMmKFvvvlGEydO1IcffqiDBw/qjTfeUGZmpmpra6+4/ZYtW7RhwwZ98cUXysnJ0Y4dOzRz5kxJUr9+/VRaWqr8/HwdPHhQTz31lF5++eWr2mdXMMICAGhTTJ+gHRQUpKSkJD355JM6ePCgLly4oKioKE2bNk2PPvqoS31ERkbqvffe05w5czR69GhVV1erd+/eSktLk4/PlccqFixYoPz8fN17772KiIjQiy++qIEDB0qSfvrTn+rBBx/UzJkzVV1drbFjx2ru3LmaP3/+1ez2FXGVEACP4iohNIeGrhJCw5rjtv9cJQQAANoEAgsAADAec1gAAICDqTNFGGEBAADGI7AAAADjEVgAAIDxCCwAAMB4BBYAAGA8AgsAADAelzUDANqWbbkt+3mjst1qPmXKFG3atEm//OUvlZeX5/TejBkz9PTTT2vy5MnauHGjJMlqtSo3N1d/+ctfdPToUYWEhKhv37668847NXnyZAUGBjbVnngUIywAABgmKipK+fn5+vbbbx3rzp8/rxdeeEG9evVyrDt06JCGDBmiN998U4sWLdKuXbtUXFys2bNn67XXXtNbb73lifKbBSMsAAAYZujQoTp48KD+9Kc/adKkSZKkP/3pT+rVq5diYmIc7e699161a9dOH330kTp27OhYHxsbq9tvv93Ym8A1BiMsAAAY6Be/+IWeffZZx+sNGzYoMzPT8bqiokJvvvmmZsyY4RRW/j+LxdLsdbYUAgsAAAa688479e677+qrr77SV199pffee0933nmn4/0DBw7Ibrfr2muvddouNDRUQUFBCgoK0pw5c1q67GbDKSEAAAzUvXt3jR07Vhs3bpTdbtfYsWMVGhp6xe127Nihuro6TZo0SdXV1S1QacsgsAAAYKhf/OIXmjlzpiRp7dq1Tu/17dtXFotFn3/+udP62NhYSVJAQEDLFNlCOCUEAICh0tLSVFNTowsXLig1NdXpvW7duunWW2/VmjVrVFVV5aEKWw6BBQAAQ/n6+mrfvn367LPP5Ovre8n7Tz/9tL777jslJiZq8+bN2rdvnz7//HP9/ve/1/79++vdprXilBCAZvPk1i88XQLQ6gUHB1/2vT59+mjXrl1atGiRsrOzdfToUfn5+WngwIGaNWuW7r333hastHlZ7F5wkbbNZlNISIgqKysb/MUCaFktGVgevLV/i30WzHf+/HkdPnxYMTEx8vf393Q5bd7lfh/ufH9zSggAABiPU0IAvIKrozmMxACtEyMsAADAeAQWAABgPAILAMBrecF1JV6hKX4PBBYAgNdp3769JOncuXMergTSD7+Hi7+XxmDSLQDA6/j6+qpz584qLy+XJAUGBnrVk4tbC7vdrnPnzqm8vFydO3e+qhvZEVgAAF4pPDxckhyhBZ7TuXNnx++jsQgsAACvZLFYFBERoR49eujChQueLqfNat++fZM8IqBRgWXt2rVatmyZrFar4uLitHr1ag0fPrzetuvXr9dzzz2nPXv2SJISEhK0aNEip/ZTpkzRpk2bnLZLTU1VQUFBY8oDAMDB19fXq56p01a5Pel28+bNysrKUk5OjkpKShQXF6fU1NTLDrkVFRVp4sSJ2rZtm4qLixUVFaXRo0fr2LFjTu3S0tJ04sQJx/Liiy82bo8AAIDXcTuwrFy5UtOmTVNmZqYGDhyovLw8BQYGasOGDfW2f/7553XvvfcqPj5eAwYM0DPPPKO6ujoVFhY6tfPz81N4eLhj6dKlS+P2CAAAeB23AktNTY127typlJSUHzrw8VFKSoqKi4td6uPcuXO6cOGCunbt6rS+qKhIPXr00LXXXqvp06eroqLisn1UV1fLZrM5LQAAwHu5FVhOnTql2tpahYWFOa0PCwuT1Wp1qY85c+YoMjLSKfSkpaXpueeeU2FhoZYsWaJ33nlHY8aMUW1tbb195ObmKiQkxLFERUW5sxsAAKCVadGrhBYvXqz8/HwVFRU5PV56woQJjp8HDRqkwYMHq0+fPioqKtItt9xyST/Z2dnKyspyvLbZbIQWAAC8mFsjLKGhofL19VVZWZnT+rKysiteX718+XItXrxYb775pgYPHtxg29jYWIWGhurAgQP1vu/n56fg4GCnBQAAeC+3AkuHDh2UkJDgNGH24gTa5OTky263dOlSLVy4UAUFBUpMTLzi5xw9elQVFRWKiIhwpzwAAOCl3L5KKCsrS+vXr9emTZu0b98+TZ8+XVVVVcrMzJQkZWRkKDs729F+yZIlmjt3rjZs2KDo6GhZrVZZrVadPXtWknT27Fk9/PDD2r59u44cOaLCwkLdfvvt6tu3r1JTU5toNwEAQGvm9hyW9PR0nTx5UvPmzZPValV8fLwKCgocE3FLS0vl4/NDDlq3bp1qamr0s5/9zKmfnJwczZ8/X76+vvrkk0+0adMmnT59WpGRkRo9erQWLlwoPz+/q9w9AADgDSx2L3j2ts1mU0hIiCorK5nPAhjkya1feLqESzx4a39PlwDg/7jz/e32KSEAAICWRmABAADG42nNABrFxNM9ALwXgQVAm+JK0GKeC2AeTgkBAADjEVgAAIDxCCwAAMB4BBYAAGA8AgsAADAegQUAABiPwAIAAIxHYAEAAMYjsAAAAOMRWAAAgPEILAAAwHgEFgAAYDwCCwAAMB6BBQAAGI/AAgAAjEdgAQAAxiOwAAAA4xFYAACA8QgsAADAeAQWAABgPAILAAAwHoEFAAAYj8ACAACMR2ABAADGI7AAAADjEVgAAIDxCCwAAMB4BBYAAGA8AgsAADAegQUAABiPwAIAAIxHYAEAAMZr5+kCAMA0T2794optHry1fwtUAuAiRlgAAIDxCCwAAMB4BBYAAGA85rAAuIQrczgAoCUxwgIAAIxHYAEAAMYjsAAAAOMRWAAAgPEILAAAwHgEFgAAYDwCCwAAMB6BBQAAGI/AAgAAjEdgAQAAxmvUrfnXrl2rZcuWyWq1Ki4uTqtXr9bw4cPrbbt+/Xo999xz2rNnjyQpISFBixYtcmpvt9uVk5Oj9evX6/Tp07rpppu0bt069evXrzHlAfAyN5T+5opttve6u8X7AtBy3B5h2bx5s7KyspSTk6OSkhLFxcUpNTVV5eXl9bYvKirSxIkTtW3bNhUXFysqKkqjR4/WsWPHHG2WLl2qp556Snl5efrggw/UsWNHpaam6vz5843fMwAA4DUsdrvd7s4GSUlJGjZsmNasWSNJqqurU1RUlO677z498sgjV9y+trZWXbp00Zo1a5SRkSG73a7IyEg99NBDmjVrliSpsrJSYWFh2rhxoyZMmHDFPm02m0JCQlRZWang4GB3dgdAPZrq4YdNNZph4gjLg7f2d+nzAFyeO9/fbo2w1NTUaOfOnUpJSfmhAx8fpaSkqLi42KU+zp07pwsXLqhr166SpMOHD8tqtTr1GRISoqSkpMv2WV1dLZvN5rQAAADv5dYcllOnTqm2tlZhYWFO68PCwrR//36X+pgzZ44iIyMdAcVqtTr6+Oc+L773z3Jzc7VgwQJ3SgdgKFdGPFqyHwBmatSk28ZavHix8vPzVVRUJH9//0b3k52draysLMdrm82mqKiopigRgIsICABakluBJTQ0VL6+viorK3NaX1ZWpvDw8Aa3Xb58uRYvXqy33npLgwcPdqy/uF1ZWZkiIiKc+oyPj6+3Lz8/P/n5+blTOgAAaMXcmsPSoUMHJSQkqLCw0LGurq5OhYWFSk5Ovux2S5cu1cKFC1VQUKDExESn92JiYhQeHu7Up81m0wcffNBgnwAAoO1w+5RQVlaWJk+erMTERA0fPlyrVq1SVVWVMjMzJUkZGRnq2bOncnNzJUlLlizRvHnz9MILLyg6OtoxLyUoKEhBQUGyWCx64IEH9MQTT6hfv36KiYnR3LlzFRkZqXHjxjXdngIAgFbL7cCSnp6ukydPat68ebJarYqPj1dBQYFj0mxpaal8fH4YuFm3bp1qamr0s5/9zKmfnJwczZ8/X5I0e/ZsVVVV6e6779bp06c1YsQIFRQUXNU8FwCNx/wUAKZx+z4sJuI+LEDTKv7tLE+XYLzkqcs9XQLQ6jXbfVgAAAA8gcACAACM16L3YQEAr7Et98ptRmU3fx1AG8EICwAAMB4jLADQCMWHKq7YJnlUCxQCtBEEFsCbcJoCgJcisABtDJcsA2iNmMMCAACMR2ABAADGI7AAAADjEVgAAIDxCCwAAMB4BBYAAGA8LmsGgObCfXGAJsMICwAAMB6BBQAAGI/AAgAAjEdgAQAAxiOwAAAA4xFYAACA8QgsAADAeAQWAABgPG4cBwCexM3lAJcwwgIAAIxHYAEAAMbjlBDQWrhy6gAAvBQjLAAAwHgEFgAAYDwCCwAAMB6BBQAAGI/AAgAAjEdgAQAAxiOwAAAA4xFYAACA8QgsAADAeAQWAABgPG7ND3iR4kMVni4BAJoFIywAAMB4BBYAAGA8AgsAADAegQUAABiPSbcAYLptuVduMyq7+esAPIjAAgDNxJWrtpJju7VAJUDrxykhAABgPAILAAAwHoEFAAAYj8ACAACMx6RbwASuXAUCAG0YIywAAMB4BBYAAGA8AgsAADAegQUAABivUYFl7dq1io6Olr+/v5KSkrRjx47Ltt27d6/Gjx+v6OhoWSwWrVq16pI28+fPl8VicVoGDBjQmNIAAIAXcjuwbN68WVlZWcrJyVFJSYni4uKUmpqq8vLyetufO3dOsbGxWrx4scLDwy/b749+9COdOHHCsbz77rvulgYAALyU24Fl5cqVmjZtmjIzMzVw4EDl5eUpMDBQGzZsqLf9sGHDtGzZMk2YMEF+fn6X7bddu3YKDw93LKGhoZdtW11dLZvN5rQAAADv5VZgqamp0c6dO5WSkvJDBz4+SklJUXFx8VUV8uWXXyoyMlKxsbGaNGmSSktLL9s2NzdXISEhjiUqKuqqPhsAAJjNrcBy6tQp1dbWKiwszGl9WFiYrFZro4tISkrSxo0bVVBQoHXr1unw4cP68Y9/rDNnztTbPjs7W5WVlY7l66+/bvRnAwAA8xlxp9sxY8Y4fh48eLCSkpLUu3dv/eEPf9DUqVMvae/n59fg6SUAAOBd3BphCQ0Nla+vr8rKypzWl5WVNTih1l2dO3dW//79deDAgSbrEwAAtF5uBZYOHTooISFBhYWFjnV1dXUqLCxUcnJykxV19uxZHTx4UBEREU3WJwAAaL3cPiWUlZWlyZMnKzExUcOHD9eqVatUVVWlzMxMSVJGRoZ69uyp3NzvH+ZWU1Ojzz77zPHzsWPHtHv3bgUFBalv376SpFmzZum2225T7969dfz4ceXk5MjX11cTJ05sqv0EAACtmNuBJT09XSdPntS8efNktVoVHx+vgoICx0Tc0tJS+fj8MHBz/PhxDRkyxPF6+fLlWr58uW6++WYVFRVJko4ePaqJEyeqoqJC3bt314gRI7R9+3Z17979KncPAAB4A4vdbrd7uoirZbPZFBISosrKSgUHB3u6HMB923KbpJviQxVN0g9aTnJst6bpaFR20/QDtCB3vr95lhAAADCeEZc1AwCukiujdIzCoBVjhAUAABiPERYA8CBX5h012TwXoBVjhAUAABiPERaguTXRFUAA0JYRWIBWgkuWAbRlnBICAADGI7AAAADjEVgAAIDxCCwAAMB4BBYAAGA8AgsAADAegQUAABiPwAIAAIxHYAEAAMYjsAAAAOMRWAAAgPEILAAAwHgEFgAAYDwCCwAAMB6BBQAAGI/AAgAAjEdgAQAAxiOwAAAA4xFYAACA8QgsAADAeAQWAABgPAILAAAwHoEFAAAYr52nCwAAtJBtuVduMyq7+esAGoERFgAAYDxGWADAcMWHKq7YJjm2WwtUAngOgQW4Gq4MsbvAlS8kAGjLOCUEAACMR2ABAADGI7AAAADjEVgAAIDxCCwAAMB4BBYAAGA8AgsAADAegQUAABiPwAIAAIxHYAEAAMYjsAAAAOPxLCEA8AI8IBHejhEWAABgPAILAAAwHqeEgGbmylA9AKBhjLAAAADjEVgAAIDxGhVY1q5dq+joaPn7+yspKUk7duy4bNu9e/dq/Pjxio6OlsVi0apVq666TwAA0La4HVg2b96srKws5eTkqKSkRHFxcUpNTVV5eXm97c+dO6fY2FgtXrxY4eHhTdInAABoW9wOLCtXrtS0adOUmZmpgQMHKi8vT4GBgdqwYUO97YcNG6Zly5ZpwoQJ8vPza5I+q6urZbPZnBYAAOC93AosNTU12rlzp1JSUn7owMdHKSkpKi4ublQBjekzNzdXISEhjiUqKqpRnw0AAFoHtwLLqVOnVFtbq7CwMKf1YWFhslqtjSqgMX1mZ2ersrLSsXz99deN+mwAANA6tMr7sPj5+V329BIAAPA+bgWW0NBQ+fr6qqyszGl9WVnZZSfUeqJPoElsy/V0BQCA/+PWKaEOHTooISFBhYWFjnV1dXUqLCxUcnJyowpojj4BAIB3cfuUUFZWliZPnqzExEQNHz5cq1atUlVVlTIzMyVJGRkZ6tmzp3Jzv//XaU1NjT777DPHz8eOHdPu3bsVFBSkvn37utQnAABo29wOLOnp6Tp58qTmzZsnq9Wq+Ph4FRQUOCbNlpaWysfnh4Gb48ePa8iQIY7Xy5cv1/Lly3XzzTerqKjIpT4BAEDbZrHb7XZPF3G1bDabQkJCVFlZqeDgYE+XA2/RRHNYePghTJEc2+3KjUZlN38hwP9x5/u7VV4lBABoJq4EdUINPICHHwIAAOMRWAAAgPE4JQQAbYQr86lcmucCeAAjLAAAwHgEFgAAYDwCCwAAMB6BBQAAGI/AAgAAjMdVQsBV4C62ANAyGGEBAADGY4QFbVMTPScIANAyGGEBAADGY4QFAOAeHpAID2CEBQAAGI/AAgAAjEdgAQAAxiOwAAAA4xFYAACA8bhKCLgM7mILAOZghAUAABiPwAIAAIxHYAEAAMZjDgsAwMGVuVvJsd1aoBLAGSMsAADAeAQWAABgPAILAAAwHnNY0CZxjxUAaF0ILAAAt7g0MXdUCxSCNoVTQgAAwHgEFgAAYDwCCwAAMB6BBQAAGI/AAgAAjEdgAQAAxiOwAAAA4xFYAACA8QgsAADAeAQWAABgPAILAAAwHoEFAAAYj8ACAACMR2ABAADGI7AAAADjEVgAAIDxCCwAAMB4BBYAAGA8AgsAADAegQUAABiPwAIAAIxHYAEAAMZrVGBZu3atoqOj5e/vr6SkJO3YsaPB9lu2bNGAAQPk7++vQYMG6fXXX3d6f8qUKbJYLE5LWlpaY0oDAABeyO3AsnnzZmVlZSknJ0clJSWKi4tTamqqysvL623//vvva+LEiZo6dap27dqlcePGady4cdqzZ49Tu7S0NJ04ccKxvPjii43bIwAA4HXcDiwrV67UtGnTlJmZqYEDByovL0+BgYHasGFDve1/9atfKS0tTQ8//LCuu+46LVy4UEOHDtWaNWuc2vn5+Sk8PNyxdOnSpXF7BAAAvI5bgaWmpkY7d+5USkrKDx34+CglJUXFxcX1blNcXOzUXpJSU1MvaV9UVKQePXro2muv1fTp01VRUXHZOqqrq2Wz2ZwWAADgvdwKLKdOnVJtba3CwsKc1oeFhclqtda7jdVqvWL7tLQ0PffccyosLNSSJUv0zjvvaMyYMaqtra23z9zcXIWEhDiWqKgod3YDAAC0Mu08XYAkTZgwwfHzoEGDNHjwYPXp00dFRUW65ZZbLmmfnZ2trKwsx2ubzUZoAQDAi7k1whIaGipfX1+VlZU5rS8rK1N4eHi924SHh7vVXpJiY2MVGhqqAwcO1Pu+n5+fgoODnRYAAOC93AosHTp0UEJCggoLCx3r6urqVFhYqOTk5Hq3SU5OdmovSVu3br1se0k6evSoKioqFBER4U55AADAS7l9lVBWVpbWr1+vTZs2ad++fZo+fbqqqqqUmZkpScrIyFB2draj/f3336+CggKtWLFC+/fv1/z58/XRRx9p5syZkqSzZ8/q4Ycf1vbt23XkyBEVFhbq9ttvV9++fZWamtpEuwkAAFozt+ewpKen6+TJk5o3b56sVqvi4+NVUFDgmFhbWloqH58fctCNN96oF154QY8//rgeffRR9evXT6+88oquv/56SZKvr68++eQTbdq0SadPn1ZkZKRGjx6thQsXys/Pr4l2EwAAtGYWu91u93QRV8tmsykkJESVlZXMZ4FLin87y9MlAF4teepyT5eAVsCd72+eJQQAAIxHYAEAAMYjsAAAAOMRWAAAgPEILAAAwHgEFgAAYDwjniUEAPAy23Kv3GZU9pXbAP+HwALv48pflACAVoVTQgAAwHgEFgAAYDwCCwAAMB5zWAAATa74UMUV2ySPaoFC4DUYYQEAAMYjsAAAAONxSghex5WhaABA68IICwAAMB6BBQAAGI/AAgAAjEdgAQAAxiOwAAAA4xFYAACA8QgsAADAeAQWAABgPAILAAAwHoEFAAAYj8ACAACMR2ABAADGI7AAAADjEVgAAIDxCCwAAMB4BBYAAGA8AgsAADAegQUAABiPwAIAAIxHYAEAAMYjsAAAAOMRWAAAgPEILAAAwHgEFgAAYDwCCwAAMB6BBQAAGI/AAgAAjEdgAQAAxiOwAAAA4xFYAACA8QgsAADAeAQWAABgPAILAAAwHoEFAAAYj8ACAACMR2ABAADGa1RgWbt2raKjo+Xv76+kpCTt2LGjwfZbtmzRgAED5O/vr0GDBun11193et9ut2vevHmKiIhQQECAUlJS9OWXXzamNAAA4IXcDiybN29WVlaWcnJyVFJSori4OKWmpqq8vLze9u+//74mTpyoqVOnateuXRo3bpzGjRunPXv2ONosXbpUTz31lPLy8vTBBx+oY8eOSk1N1fnz5xu/ZwAAwGtY7Ha73Z0NkpKSNGzYMK1Zs0aSVFdXp6ioKN1333165JFHLmmfnp6uqqoqvfbaa451N9xwg+Lj45WXlye73a7IyEg99NBDmjVrliSpsrJSYWFh2rhxoyZMmHDFmmw2m0JCQlRZWang4GB3dgdeqPi3szxdAgAXJE9d7ukS4GHufH+3c6fjmpoa7dy5U9nZ2Y51Pj4+SklJUXFxcb3bFBcXKysry2ldamqqXnnlFUnS4cOHZbValZKS4ng/JCRESUlJKi4urjewVFdXq7q62vG6srJS0vc7DlR9W33lRgA8jr+zcfHPgCtjJ24FllOnTqm2tlZhYWFO68PCwrR///56t7FarfW2t1qtjvcvrrtcm3+Wm5urBQsWXLI+KirKtR0BAHjefWs8XQEMcebMGYWEhDTYxq3AYors7GynUZu6ujp988036tatmywWi0dqstlsioqK0tdff81pqXpwfBrG8bk8jk3DOD4N4/g0zNPHx26368yZM4qMjLxiW7cCS2hoqHx9fVVWVua0vqysTOHh4fVuEx4e3mD7i/8tKytTRESEU5v4+Ph6+/Tz85Ofn5/Tus6dO7uzK80mODiY/ykawPFpGMfn8jg2DeP4NIzj0zBPHp8rjaxc5NZVQh06dFBCQoIKCwsd6+rq6lRYWKjk5OR6t0lOTnZqL0lbt251tI+JiVF4eLhTG5vNpg8++OCyfQIAgLbF7VNCWVlZmjx5shITEzV8+HCtWrVKVVVVyszMlCRlZGSoZ8+eys3NlSTdf//9uvnmm7VixQqNHTtW+fn5+uijj/Sb3/xGkmSxWPTAAw/oiSeeUL9+/RQTE6O5c+cqMjJS48aNa7o9BQAArZbbgSU9PV0nT57UvHnzZLVaFR8fr4KCAsek2dLSUvn4/DBwc+ONN+qFF17Q448/rkcffVT9+vXTK6+8ouuvv97RZvbs2aqqqtLdd9+t06dPa8SIESooKJC/v38T7GLL8PPzU05OziWnqvA9jk/DOD6Xx7FpGMenYRyfhrWm4+P2fVgAAABaGs8SAgAAxiOwAAAA4xFYAACA8QgsAADAeASWZvKXv/xFSUlJCggIUJcuXbhEux7V1dWKj4+XxWLR7t27PV2OEY4cOaKpU6cqJiZGAQEB6tOnj3JyclRTU+Pp0jxm7dq1io6Olr+/v5KSkrRjxw5Pl2SE3NxcDRs2TJ06dVKPHj00btw4ff75554uy0iLFy923EID3zt27JjuvPNOdevWTQEBARo0aJA++ugjT5fVIAJLM3jppZd01113KTMzUx9//LHee+89/cd//IenyzLO7NmzXbodc1uyf/9+1dXV6de//rX27t2rJ598Unl5eXr00Uc9XZpHbN68WVlZWcrJyVFJSYni4uKUmpqq8vJyT5fmce+8845mzJih7du3a+vWrbpw4YJGjx6tqqoqT5dmlA8//FC//vWvNXjwYE+XYox//OMfuummm9S+fXv99a9/1WeffaYVK1aoS5cuni6tYXY0qQsXLth79uxpf+aZZzxditFef/11+4ABA+x79+61S7Lv2rXL0yUZa+nSpfaYmBhPl+ERw4cPt8+YMcPxura21h4ZGWnPzc31YFVmKi8vt0uyv/POO54uxRhnzpyx9+vXz75161b7zTffbL///vs9XZIR5syZYx8xYoSny3AbIyxNrKSkRMeOHZOPj4+GDBmiiIgIjRkzRnv27PF0acYoKyvTtGnT9Lvf/U6BgYGeLsd4lZWV6tq1q6fLaHE1NTXauXOnUlJSHOt8fHyUkpKi4uJiD1ZmpsrKSklqk39WLmfGjBkaO3as058hSK+++qoSExN1xx13qEePHhoyZIjWr1/v6bKuiMDSxA4dOiRJmj9/vh5//HG99tpr6tKli0aOHKlvvvnGw9V5nt1u15QpU3TPPfcoMTHR0+UY78CBA1q9erV++ctferqUFnfq1CnV1tY67qJ9UVhYmKxWq4eqMlNdXZ0eeOAB3XTTTU53EW/L8vPzVVJS4nhMDH5w6NAhrVu3Tv369dMbb7yh6dOn67/+67+0adMmT5fWIAKLix555BFZLJYGl4vzDyTpscce0/jx45WQkKBnn31WFotFW7Zs8fBeNB9Xj8/q1at15swZZWdne7rkFuXq8fn/jh07prS0NN1xxx2aNm2ahypHazBjxgzt2bNH+fn5ni7FCF9//bXuv/9+Pf/8863qES8tpa6uTkOHDtWiRYs0ZMgQ3X333Zo2bZry8vI8XVqD3H6WUFv10EMPacqUKQ22iY2N1YkTJyRJAwcOdKz38/NTbGysSktLm7NEj3L1+Lz99tsqLi6+5LkViYmJmjRpkvEJv7FcPT4XHT9+XKNGjdKNN97oeFBoWxMaGipfX1+VlZU5rS8rK1N4eLiHqjLPzJkz9dprr+lvf/ubrrnmGk+XY4SdO3eqvLxcQ4cOdayrra3V3/72N61Zs0bV1dXy9fX1YIWeFRER4fQdJUnXXXedXnrpJQ9V5BoCi4u6d++u7t27X7FdQkKC/Pz89Pnnn2vEiBGSpAsXLujIkSPq3bt3c5fpMa4en6eeekpPPPGE4/Xx48eVmpqqzZs3KykpqTlL9ChXj4/0/cjKqFGjHKNz//9hom1Jhw4dlJCQoMLCQsdtAerq6lRYWKiZM2d6tjgD2O123XfffXr55ZdVVFSkmJgYT5dkjFtuuUWffvqp07rMzEwNGDBAc+bMadNhRZJuuummSy6B/+KLL4z/jiKwNLHg4GDdc889ysnJUVRUlHr37q1ly5ZJku644w4PV+d5vXr1cnodFBQkSerTpw//OtT3YWXkyJHq3bu3li9frpMnTzrea4ujCllZWZo8ebISExM1fPhwrVq1SlVVVcrMzPR0aR43Y8YMvfDCC/rzn/+sTp06Oeb1hISEKCAgwMPVeVanTp0umcvTsWNHdevWjTk+kh588EHdeOONWrRokX7+859rx44d+s1vfmP8aC6BpRksW7ZM7dq101133aVvv/1WSUlJevvtt82/xh0et3XrVh04cEAHDhy4JMDZ2+CD1dPT03Xy5EnNmzdPVqtV8fHxKigouGQiblu0bt06SdLIkSOd1j/77LNXPP2Itm3YsGF6+eWXlZ2drf/+7/9WTEyMVq1apUmTJnm6tAZZ7G3xb0EAANCqtM2T4wAAoFUhsAAAAOMRWAAAgPEILAAAwHgEFgAAYDwCCwAAMB6BBQAAGI/AAgAAjEdgAXDVRo4cqQceeMDl9hs3blTnzp2brZ6GHDlyRBaLRbt3775sG0/WB6B+BBYAHjd//nzFx8d7ugyH9PR0ffHFF54uA8D/w7OEAOCfBAQEtPkHCAKmYYQFgFuqqqqUkZGhoKAgRUREaMWKFZe0qa6u1qxZs9SzZ0917NhRSUlJKioqqre/jRs3asGCBfr4449lsVhksVi0ceNGSdLKlSs1aNAgdezYUVFRUbr33nt19uzZBuuzWCxat26dxowZo4CAAMXGxuqPf/zjJe0OHTqkUaNGKTAwUHFxcSouLnaqiVNCgFkILADc8vDDD+udd97Rn//8Z7355psqKipSSUmJU5uZM2equLhY+fn5+uSTT3THHXcoLS1NX3755SX9paen66GHHtKPfvQjnThxQidOnFB6erokycfHR0899ZT27t2rTZs26e2339bs2bOvWOPcuXM1fvx4ffzxx5o0aZImTJigffv2ObV57LHHNGvWLO3evVv9+/fXxIkT9d13313FkQHQrOwA4KIzZ87YO3ToYP/DH/7gWFdRUWEPCAiw33///Xa73W7/6quv7L6+vvZjx445bXvLLbfYs7Oz7Xa73f7ss8/aQ0JCHO/l5OTY4+Lirvj5W7ZssXfr1q3BNpLs99xzj9O6pKQk+/Tp0+12u91++PBhuyT7M88843h/7969dkn2ffv21VsfAM9jDgsAlx08eFA1NTVKSkpyrOvatauuvfZax+tPP/1UtbW16t+/v9O21dXV6tatm1uf99Zbbyk3N1f79++XzWbTd999p/Pnz+vcuXMKDAy87HbJycmXvP7nq4IGDx7s+DkiIkKSVF5ergEDBrhVI4CWQWAB0KTOnj0rX19f7dy5U76+vk7vBQUFudzPkSNH9G//9m+aPn26/ud//kddu3bVu+++q6lTp6qmpqbBwOKK9u3bO362WCySpLq6uqvqE0DzYQ4LAJf16dNH7du31wcffOBY949//MPpEuAhQ4aotrZW5eXl6tu3r9MSHh5eb78dOnRQbW2t07qdO3eqrq5OK1as0A033KD+/fvr+PHjLtW5ffv2S15fd911ru4mAAMxwgLAZUFBQZo6daoefvhhdevWTT169NBjjz0mH58f/u3Tv39/TZo0SRkZGVqxYoWGDBmikydPqrCwUIMHD9bYsWMv6Tc6OlqHDx/W7t27dc0116hTp07q27evLly4oNWrV+u2227Te++9p7y8PJfq3LJlixITEzVixAg9//zz2rFjh37729822XEA0PIYYQHglmXLlunHP/6xbrvtNqWkpGjEiBFKSEhwavPss88qIyNDDz30kK699lqNGzdOH374oXr16lVvn+PHj1daWppGjRql7t2768UXX1RcXJxWrlypJUuW6Prrr9fzzz+v3Nxcl2pcsGCB8vPzNXjwYD333HN68cUXNXDgwKvedwCeY7Hb7XZPFwEATcVisejll1/WuHHjPF0KgCbECAsAADAegQUAABiPSbcAvApnuQHvxAgLAAAwHoEFAAAYj8ACAACMR2ABAADGI7AAAADjEVgAAIDxCCwAAMB4BBYAAGC8/wULXw4Ndda1TQAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAjUAAAGwCAYAAABRgJRuAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8hTgPZAAAACXBIWXMAAA9hAAAPYQGoP6dpAAA5NklEQVR4nO3dfVxVZb7///cGBbwDEgTEULxLbbxBERCbk5pMWE3FRKaOBRlpmZi6pxmlvKuZMzhFxlQWeSZvzjkxOs6kzVhRSlqdxDuUTEsmnZRUQM0fkDiCsvfvD7/t2gnIRnTD5ev5eOxH7LWuda3PWir73bWutbbFbrfbBQAA0MJ5uLsAAACApkCoAQAARiDUAAAAIxBqAACAEQg1AADACIQaAABgBEINAAAwQit3F3C12Gw2HTt2TB06dJDFYnF3OQAAoAHsdru+/fZbhYaGysOj/rGYaybUHDt2TGFhYe4uAwAANMLXX3+t66+/vt4210yo6dChg6QLJ8XX19fN1QAAgIaoqKhQWFiY43O8PtdMqPnukpOvry+hBgCAFqYhU0eYKAwAAIxAqAEAAEYg1AAAACNcM3NqAACoS01Njc6dO+fuMq5JrVu3lqenZ5P0RagBAFyz7Ha7SkpKVFZW5u5Srmn+/v4KCQm57OfINSrULFmyRM8995xKSko0aNAgvfTSS4qOjq617b59+zR//nzl5+fr8OHDeuGFFzRz5kynNuHh4Tp8+PBF2z722GNasmSJJGnkyJH68MMPndY/8sgjysrKaswhAADgCDRBQUFq27YtD2e9yux2u86cOaPjx49Lkjp37nxZ/bkcalavXi2r1aqsrCzFxMQoMzNT8fHxKiwsVFBQ0EXtz5w5ox49emjs2LGaNWtWrX3u2LFDNTU1jvd79+7Vz372M40dO9ap3eTJk/XMM8843rdt29bV8gEAkHThktN3gSYgIMDd5Vyz2rRpI0k6fvy4goKCLutSlMsThRcvXqzJkydr0qRJuvHGG5WVlaW2bdtq2bJltbaPiorSc889p/Hjx8vb27vWNp06dVJISIjjtX79evXs2VMjRoxwate2bVundjxvBgDQWN/NoeF/kN3vuz+Dy53X5FKoqa6uVn5+vuLi4r7vwMNDcXFxysvLu6xCfriP//3f/9VDDz100TDgG2+8ocDAQPXv319paWk6c+ZMnf1UVVWpoqLC6QUAwI9xycn9murPwKXLTydPnlRNTY2Cg4OdlgcHB2v//v1NUtC6detUVlamBx980Gn5L3/5S3Xr1k2hoaHas2ePZs+ercLCQr355pu19pOenq6nn366SWoCAADNX7O7++n111/XbbfdptDQUKflU6ZMcfw8YMAAde7cWaNHj9bBgwfVs2fPi/pJS0uT1Wp1vP/uuyMAAICZXAo1gYGB8vT0VGlpqdPy0tJShYSEXHYxhw8f1saNG+scffmhmJgYSdKBAwdqDTXe3t51zuEBAKA+L2z451Xb16yf3dDkfVosFq1du1YJCQlN3ndz5tKcGi8vL0VGRio3N9exzGazKTc3V7GxsZddzPLlyxUUFKQ77rjjkm0LCgokXf7tXwAAtDQnTpzQ1KlT1bVrV3l7eyskJETx8fH65JNP3F2aW7l8+clqtSo5OVlDhw5VdHS0MjMzVVlZqUmTJkmSkpKS1KVLF6Wnp0u6MPH3888/d/x89OhRFRQUqH379urVq5ejX5vNpuXLlys5OVmtWjmXdfDgQWVnZ+v2229XQECA9uzZo1mzZunmm2/WwIEDG33wAAC0RImJiaqurtbKlSvVo0cPlZaWKjc3V998880V22d1dbW8vLyuWP9NweVbuseNG6eMjAzNnz9fERERKigoUE5OjmPycFFRkYqLix3tjx07psGDB2vw4MEqLi5WRkaGBg8erIcfftip340bN6qoqEgPPfTQRfv08vLSxo0bdeutt6pv37761a9+pcTERP3jH/9wtXwAAFq0srIyffzxx/rDH/6gUaNGqVu3boqOjlZaWpruuusuR7uTJ0/qF7/4hdq2bavevXvr73//u1M/e/fu1W233ab27dsrODhYDzzwgE6ePOlYP3LkSKWmpmrmzJkKDAxUfHy8pAuXtl599VXddtttatOmjXr06KG//vWvTn3Pnj1bN9xwg9q2basePXpo3rx5V+VrKCx2u91+xffSDFRUVMjPz0/l5eU83wYAoLNnz+qrr75S9+7d5ePj47SuOcypKa04W+vy8+fPq0+3zpqY9KBeXJxR6/xRi8Wi66+/Xs8++6yioqL00ksvadmyZTp8+LA6duyosrIy3XDDDXr44YeVlJSkf//735o9e7bOnz+vDz74QNKFUJOfn6+pU6cqJSVFktSnTx9ZLBYFBARo0aJFuvnmm/U///M/Sk9P12effaZ+/fpJkn73u9/plltuUWhoqD777DNNnjxZVqtVv/nNb2o9pvr+LFz5/CbUAACuSS011EjS+rfW6onHp+ns2X9ryJAhGjFihMaPH++YkmGxWDR37lz99re/lSRVVlaqffv2evfddzVmzBj97ne/08cff6z33nvP0eeRI0cUFhamwsJC3XDDDRo5cqQqKiq0a9cup31bLBY9+uijevXVVx3Lhg0bpiFDhuiVV16ptd6MjAytWrVKO3furHV9U4Ualy8/AQAA9/r53b9QQeG/9Pe//11jxozR5s2bNWTIEK1YscLR5odzTtu1aydfX1/Hdyx9+umn2rRpk9q3b+949e3bV9KFeazfiYyMrHX/P745KDY2Vl988YXj/erVq3XTTTcpJCRE7du319y5c1VUVHTZx30phBoAAFogHx8f/exnP9O8efO0ZcsWPfjgg1qwYIFjfevWrZ3aWywW2Ww2SdLp06d15513qqCgwOn15Zdf6uabb3Zs065dO5frysvL08SJE3X77bdr/fr12r17t5566ilVV1c38kgbrtk9fA8AALjuxhtv1Lp16xrUdsiQIfrb3/6m8PDwi+44boitW7cqKSnJ6f3gwYMlSVu2bFG3bt301FNPOdYfPnzY5X00BiM1AAC0IKdOfaPEn4/RX1f/WXv27NFXX32lNWvW6Nlnn9Xdd9/doD6mTZumU6dOacKECdqxY4cOHjyo9957T5MmTVJNTc0lt1+zZo2WLVumf/7zn1qwYIG2b9+u1NRUSVLv3r1VVFSkVatW6eDBg3rxxRe1du3ayzrmhmKkBgCAH7kST/ltKu3atdeQoVFauuQlPfXrf+ncuXMKCwvT5MmT9eSTTzaoj9DQUH3yySeaPXu2br31VlVVValbt24aM2aMPDwuPd7x9NNPa9WqVXrsscfUuXNn/fnPf9aNN94oSbrrrrs0a9YspaamqqqqSnfccYfmzZunhQsXXs5hNwh3PwEArkn13XHTHNR399N3gn2vft1X4isYmuruJ0ZqcNkacutjc/6/HgAX8G8ZLR1zagAAgBEYqQEAQM33ck9z05xnrTBSAwAAjECoAQAARuDyEwCgWWLiMlxFqLmWbUq/dJtRaVe+DgAAmgCXnwAAgBEYqQEAF7XIyyINGZlV4hUvA7iSCDUA0MI1KGQ1t9/2zTxktdvyXO0rvK/AiWzEZf7Hp07WX7L/V4888oiysrKc1k2bNk2vvPKKkpOTtWLFCklSSUmJ0tPT9fbbb+vIkSPy8/NTr169dP/99ys5OVlt27ZtiiNxu+b21xzXsBb5f78Amr26fre08ajR4OvO6eTpKrWultpVnXCs86qprKM3vytQYeN0uf56rVq1Si+88ILatGkj6cLXDWRnZ6tr166Odv/617900003yd/fX7///e81YMAAeXt767PPPtPSpUvVpUsX3XXXXe46jCZFqGlmruUP9mFFSxvQKuOK19GiMfn7quDvKpqDAYMidOTwIb355puaOHGiJOnNN99U165d1b17d0e7xx57TK1atdLOnTvVrl07x/IePXro7rvvbvDD9FrCwwkJNS3QtRx8gMtC6INhxv7yAb32X68r7s4Ll+qylv5J9064X1v+7yP9+1yNvjh0VO+//75+//vfOwWaH7JYLFez5CuKUINrUwM+3F44f+nr+YTHy3c1Q3rev765ZJvYUU2yq4YhZOEyJY6boN8/PV9fFx2WJO3Ylqes5f+tLf/3kSTp0L8Oym63q0+fPk7bBQYG6uzZCyMv06ZN0x/+8IerW/gVQqhpKvxyuia1xFGzllgzgNoFBnZS3K1jtDr7f2W32zX61jEKCAi8qF3ZmWqny0fv5H4sm82mxyY/qP/v27rmD7U8hBoAV0xDAhSAyzP+gWQ9+cQsSVL685lO68J79JTFYtHBL53/LXb7f3NufP7fBGNTEGrQsjAihjo0JEANuwp1fKdF3mbdAIz0NT+3xN2qc+eqZbFYNGr0z5zWdewYoBGjRmvZf2XpoUceq3NejSla4D+pFuxqPpehQfsyk6kfJi1Rw+4SaoBNAQ1odO0+OK5B57lHQ86hmeo8P94d5O37M7WpPiVvexP+Uqj69tJtvDs02e48PT318fYCx88/tuj5P+rO+Fs0ZuRN+tWcp3Rj/wHy8PBQwa58HfjnPzUwYnCT1eJu/Gq/ihoySVFdL92kueH21vo1LGT97dIdMQJ1dTS3/yFobvWgafww+FQU19HougZ318HXt851P+nSQVty1ysjc4kWLXxKR4tL5O3lpb59emvG1Ic1eVJSg/fT3BFqgDo01SjD1q5TLtmm2d2Vg2ajQf8zhCZXHT2t1uVencIuvXGdIcU1P3wY4I+9nvl7SVJdU3xXZK9xeh8SHKyM9GeUkf7MFannew04P1cQoQa4xjCyBrjf6arz7i7BWRMFMXcj1AAtRXO7DHEV6zH10m1LRChGc0aoaSJNNUTckF8YDbmc0WSa6oOruX0gX0VNNlkWwMWu9u8WQ0Y0TEWowVXBvADzNLc/U8KjebirC64i1FzDGjQ5tZn9wmiqyxDN7QMZzQd/N1qWy/7zstulhn2fI66ghn6p5qUQagBc7Bq+XHgta7rnCrWQvz/n/i2brUZnq8/J26tlfhw27I6k5u/MmTOSpNatW19WPy3zT/Eax5As0Pzx77QFsJ3XuZMHdbK1l6SO8vFqLV3iC6tbWZpmRKHqXE2T9NNUGnJcDam51dmzl2zzQ3a7XWfOnNHx48fl7+9f68MDXUGoQb0Yigfgqpb0e8NWuk9nJZWe6ykPD0/JUn+q8W7l0ST7rTpva5J+mhvvctdCzXf8/f0VEhJy2fsn1AAArmm20n2qOlEotb70lzv2DfNvkn3u/rqsSfppbvreO9vlbVq3bn3ZIzTfIdQAAGA736DvbPKxeV2yTUsaqWpqPj4+bt0/ocZQ1/I/Klw+/v4AaIma5uIgAACAmzFSAwBuwogY0LQINUAL0RIflggAV1OjLj8tWbJE4eHh8vHxUUxMjLZv315n23379ikxMVHh4eGyWCzKzMy8qM3ChQtlsVicXn379nVqc/bsWU2bNk0BAQFq3769EhMTVVpa2pjyYbhhRUsv+QIAmMflkZrVq1fLarUqKytLMTExyszMVHx8vAoLCxUUFHRR+zNnzqhHjx4aO3asZs2aVWe/P/nJT7Rx48bvC2vlXNqsWbP09ttva82aNfLz81NqaqruueceffLJJ64eAgAAjcIlw+bN5VCzePFiTZ48WZMmTZIkZWVl6e2339ayZcs0Z86ci9pHRUUpKipKkmpd7yikVas6H7xTXl6u119/XdnZ2brlllskScuXL1e/fv20detWDRs27KJtqqqqVFVV5XhfUVHR8IMEAAAtjkuXn6qrq5Wfn6+4uLjvO/DwUFxcnPLy8i6rkC+//FKhoaHq0aOHJk6cqKKiIse6/Px8nTt3zmm/ffv2VdeuXevcb3p6uvz8/ByvsLCwy6oPAAA0by6N1Jw8eVI1NTUKDg52Wh4cHKz9+/c3uoiYmBitWLFCffr0UXFxsZ5++mn9x3/8h/bu3asOHTqopKREXl5e8vf3v2i/JSUltfaZlpYmq9XqeF9RUUGwAXDN4rIJrgXN4u6n2267zfHzwIEDFRMTo27duukvf/mLUlJSGtWnt7e3vL29m6pEAADQzLl0+SkwMFCenp4X3XVUWlraJF9E9R1/f3/dcMMNOnDggCQpJCRE1dXVKisru6L7BQAALZdLocbLy0uRkZHKzc11LLPZbMrNzVVsbGyTFXX69GkdPHhQnTt3liRFRkaqdevWTvstLCxUUVFRk+4XAAC0XC5ffrJarUpOTtbQoUMVHR2tzMxMVVZWOu6GSkpKUpcuXZSeni7pwuTizz//3PHz0aNHVVBQoPbt26tXr16SpCeeeEJ33nmnunXrpmPHjmnBggXy9PTUhAkTJEl+fn5KSUmR1WpVx44d5evrq+nTpys2NrbWO5+AaxXzJgBcy1wONePGjdOJEyc0f/58lZSUKCIiQjk5OY7Jw0VFRfLw+H4A6NixYxo8eLDjfUZGhjIyMjRixAht3rxZknTkyBFNmDBB33zzjTp16qSf/vSn2rp1qzp16uTY7oUXXpCHh4cSExNVVVWl+Ph4vfLKK409bgAAYBiL3W63u7uIq6GiokJ+fn4qLy+Xr69vk/ef9/oTTd4nAAAtSWxKRpP36crnN9/SDQAAjECoAQAARiDUAAAAIxBqAACAEQg1AADACIQaAABgBEINAAAwAqEGAAAYgVADAACMQKgBAABGINQAAAAjEGoAAIARCDUAAMAIhBoAAGAEQg0AADACoQYAABiBUAMAAIxAqAEAAEYg1AAAACMQagAAgBEINQAAwAiEGgAAYARCDQAAMAKhBgAAGIFQAwAAjECoAQAARiDUAAAAIxBqAACAEQg1AADACIQaAABgBEINAAAwAqEGAAAYgVADAACMQKgBAABGINQAAAAjEGoAAIARCDUAAMAIhBoAAGAEQg0AADACoQYAABihUaFmyZIlCg8Pl4+Pj2JiYrR9+/Y62+7bt0+JiYkKDw+XxWJRZmbmRW3S09MVFRWlDh06KCgoSAkJCSosLHRqM3LkSFksFqfXo48+2pjyAQCAgVwONatXr5bVatWCBQu0a9cuDRo0SPHx8Tp+/Hit7c+cOaMePXpo0aJFCgkJqbXNhx9+qGnTpmnr1q3asGGDzp07p1tvvVWVlZVO7SZPnqzi4mLH69lnn3W1fAAAYKhWrm6wePFiTZ48WZMmTZIkZWVl6e2339ayZcs0Z86ci9pHRUUpKipKkmpdL0k5OTlO71esWKGgoCDl5+fr5ptvdixv27ZtncHox6qqqlRVVeV4X1FR0aDtAABAy+TSSE11dbXy8/MVFxf3fQceHoqLi1NeXl6TFVVeXi5J6tixo9PyN954Q4GBgerfv7/S0tJ05syZOvtIT0+Xn5+f4xUWFtZk9QEAgObHpZGakydPqqamRsHBwU7Lg4ODtX///iYpyGazaebMmbrpppvUv39/x/Jf/vKX6tatm0JDQ7Vnzx7Nnj1bhYWFevPNN2vtJy0tTVar1fG+oqKCYAMAgMFcvvx0pU2bNk179+7V//3f/zktnzJliuPnAQMGqHPnzho9erQOHjyonj17XtSPt7e3vL29r3i9AACgeXDp8lNgYKA8PT1VWlrqtLy0tLTBc13qk5qaqvXr12vTpk26/vrr620bExMjSTpw4MBl7xcAALR8LoUaLy8vRUZGKjc317HMZrMpNzdXsbGxjS7CbrcrNTVVa9eu1QcffKDu3btfcpuCggJJUufOnRu9XwAAYA6XLz9ZrVYlJydr6NChio6OVmZmpiorKx13QyUlJalLly5KT0+XdGFy8eeff+74+ejRoyooKFD79u3Vq1cvSRcuOWVnZ+utt95Shw4dVFJSIkny8/NTmzZtdPDgQWVnZ+v2229XQECA9uzZo1mzZunmm2/WwIEDm+REAACAls3lUDNu3DidOHFC8+fPV0lJiSIiIpSTk+OYPFxUVCQPj+8HgI4dO6bBgwc73mdkZCgjI0MjRozQ5s2bJUmvvvqqpAsP2Puh5cuX68EHH5SXl5c2btzoCFBhYWFKTEzU3LlzXS0fAAAYymK32+3uLuJqqKiokJ+fn8rLy+Xr69vk/ee9/kST9wkAQEsSm5LR5H268vnNdz8BAAAjEGoAAIARCDUAAMAIhBoAAGAEQg0AADACoQYAABiBUAMAAIxAqAEAAEYg1AAAACMQagAAgBEINQAAwAiEGgAAYARCDQAAMAKhBgAAGIFQAwAAjECoAQAARiDUAAAAIxBqAACAEQg1AADACIQaAABgBEINAAAwAqEGAAAYgVADAACMQKgBAABGINQAAAAjEGoAAIARCDUAAMAIhBoAAGAEQg0AADACoQYAABiBUAMAAIxAqAEAAEYg1AAAACMQagAAgBEINQAAwAiEGgAAYARCDQAAMAKhBgAAGIFQAwAAjECoAQAARmhUqFmyZInCw8Pl4+OjmJgYbd++vc62+/btU2JiosLDw2WxWJSZmdmoPs+ePatp06YpICBA7du3V2JiokpLSxtTPgAAMJDLoWb16tWyWq1asGCBdu3apUGDBik+Pl7Hjx+vtf2ZM2fUo0cPLVq0SCEhIY3uc9asWfrHP/6hNWvW6MMPP9SxY8d0zz33uFo+AAAwlMVut9td2SAmJkZRUVF6+eWXJUk2m01hYWGaPn265syZU++24eHhmjlzpmbOnOlSn+Xl5erUqZOys7N17733SpL279+vfv36KS8vT8OGDbtoX1VVVaqqqnK8r6ioUFhYmMrLy+Xr6+vKITdI3utPNHmfAAC0JLEpGU3eZ0VFhfz8/Br0+e3SSE11dbXy8/MVFxf3fQceHoqLi1NeXl6jim1In/n5+Tp37pxTm759+6pr16517jc9PV1+fn6OV1hYWKPqAwAALYNLoebkyZOqqalRcHCw0/Lg4GCVlJQ0qoCG9FlSUiIvLy/5+/s3eL9paWkqLy93vL7++utG1QcAAFqGVu4u4Erx9vaWt7e3u8sAAABXiUsjNYGBgfL09LzorqPS0tI6JwE3RZ8hISGqrq5WWVlZk+0XAACYxaVQ4+XlpcjISOXm5jqW2Ww25ebmKjY2tlEFNKTPyMhItW7d2qlNYWGhioqKGr1fAABgFpcvP1mtViUnJ2vo0KGKjo5WZmamKisrNWnSJElSUlKSunTpovT0dEkXJgJ//vnnjp+PHj2qgoICtW/fXr169WpQn35+fkpJSZHValXHjh3l6+ur6dOnKzY2ttY7nwAAwLXH5VAzbtw4nThxQvPnz1dJSYkiIiKUk5PjmOhbVFQkD4/vB4COHTumwYMHO95nZGQoIyNDI0aM0ObNmxvUpyS98MIL8vDwUGJioqqqqhQfH69XXnmlsccNAAAM4/JzaloqV+5zbwyeUwMAuNa1qOfUAAAANFeEGgAAYARCDQAAMAKhBgAAGIFQAwAAjECoAQAARiDUAAAAIxBqAACAEQg1AADACIQaAABgBEINAAAwAqEGAAAYgVADAACMQKgBAABGINQAAAAjEGoAAIARCDUAAMAIhBoAAGAEQg0AADACoQYAABiBUAMAAIxAqAEAAEYg1AAAACMQagAAgBEINQAAwAiEGgAAYARCDQAAMAKhBgAAGIFQAwAAjECoAQAARiDUAAAAIxBqAACAEQg1AADACIQaAABgBEINAAAwAqEGAAAYgVADAACMQKgBAABGINQAAAAjEGoAAIARGhVqlixZovDwcPn4+CgmJkbbt2+vt/2aNWvUt29f+fj4aMCAAXrnnXec1lssllpfzz33nKNNeHj4ResXLVrUmPIBAICBXA41q1evltVq1YIFC7Rr1y4NGjRI8fHxOn78eK3tt2zZogkTJiglJUW7d+9WQkKCEhIStHfvXkeb4uJip9eyZctksViUmJjo1Nczzzzj1G769Omulg8AAAxlsdvtdlc2iImJUVRUlF5++WVJks1mU1hYmKZPn645c+Zc1H7cuHGqrKzU+vXrHcuGDRumiIgIZWVl1bqPhIQEffvtt8rNzXUsCw8P18yZMzVz5swG1VlVVaWqqirH+4qKCoWFham8vFy+vr4N6sMVea8/0eR9AgDQksSmZDR5nxUVFfLz82vQ57dLIzXV1dXKz89XXFzc9x14eCguLk55eXm1bpOXl+fUXpLi4+PrbF9aWqq3335bKSkpF61btGiRAgICNHjwYD333HM6f/58nbWmp6fLz8/P8QoLC2vIIQIAgBaqlSuNT548qZqaGgUHBzstDw4O1v79+2vdpqSkpNb2JSUltbZfuXKlOnTooHvuucdp+eOPP64hQ4aoY8eO2rJli9LS0lRcXKzFixfX2k9aWpqsVqvj/XcjNQAAwEwuhZqrYdmyZZo4caJ8fHyclv8woAwcOFBeXl565JFHlJ6eLm9v74v68fb2rnU5AAAwk0uXnwIDA+Xp6anS0lKn5aWlpQoJCal1m5CQkAa3//jjj1VYWKiHH374krXExMTo/PnzOnToUMMPAAAAGMulUOPl5aXIyEinCbw2m025ubmKjY2tdZvY2Fin9pK0YcOGWtu//vrrioyM1KBBgy5ZS0FBgTw8PBQUFOTKIQAAAEO5fPnJarUqOTlZQ4cOVXR0tDIzM1VZWalJkyZJkpKSktSlSxelp6dLkmbMmKERI0bo+eef1x133KFVq1Zp586dWrp0qVO/FRUVWrNmjZ5//vmL9pmXl6dt27Zp1KhR6tChg/Ly8jRr1izdf//9uu666xpz3AAAwDAuh5px48bpxIkTmj9/vkpKShQREaGcnBzHZOCioiJ5eHw/ADR8+HBlZ2dr7ty5evLJJ9W7d2+tW7dO/fv3d+p31apVstvtmjBhwkX79Pb21qpVq7Rw4UJVVVWpe/fumjVrltM8GwAAcG1z+Tk1LZUr97k3Bs+pAQBc61rUc2oAAACaK0INAAAwAqEGAAAYgVADAACMQKgBAABGINQAAAAjEGoAAIARCDUAAMAIhBoAAGAEQg0AADACoQYAABiBUAMAAIxAqAEAAEYg1AAAACMQagAAgBEINQAAwAiEGgAAYARCDQAAMAKhBgAAGIFQAwAAjECoAQAARiDUAAAAIxBqAACAEQg1AADACIQaAABgBEINAAAwAqEGAAAYgVADAACMQKgBAABGINQAAAAjEGoAAIARCDUAAMAIhBoAAGAEQg0AADACoQYAABiBUAMAAIxAqAEAAEYg1AAAACMQagAAgBEINQAAwAiNCjVLlixReHi4fHx8FBMTo+3bt9fbfs2aNerbt698fHw0YMAAvfPOO07rH3zwQVksFqfXmDFjnNqcOnVKEydOlK+vr/z9/ZWSkqLTp083pnwAAGAgl0PN6tWrZbVatWDBAu3atUuDBg1SfHy8jh8/Xmv7LVu2aMKECUpJSdHu3buVkJCghIQE7d2716ndmDFjVFxc7Hj9+c9/dlo/ceJE7du3Txs2bND69ev10UcfacqUKa6WDwAADGWx2+12VzaIiYlRVFSUXn75ZUmSzWZTWFiYpk+frjlz5lzUfty4caqsrNT69esdy4YNG6aIiAhlZWVJujBSU1ZWpnXr1tW6zy+++EI33nijduzYoaFDh0qScnJydPvtt+vIkSMKDQ29ZN0VFRXy8/NTeXm5fH19XTnkBsl7/Ykm7xMAgJYkNiWjyft05fPbpZGa6upq5efnKy4u7vsOPDwUFxenvLy8WrfJy8tzai9J8fHxF7XfvHmzgoKC1KdPH02dOlXffPONUx/+/v6OQCNJcXFx8vDw0LZt22rdb1VVlSoqKpxeAADAXC6FmpMnT6qmpkbBwcFOy4ODg1VSUlLrNiUlJZdsP2bMGP33f/+3cnNz9Yc//EEffvihbrvtNtXU1Dj6CAoKcuqjVatW6tixY537TU9Pl5+fn+MVFhbmyqECAIAWppW7C5Ck8ePHO34eMGCABg4cqJ49e2rz5s0aPXp0o/pMS0uT1Wp1vK+oqCDYAABgMJdGagIDA+Xp6anS0lKn5aWlpQoJCal1m5CQEJfaS1KPHj0UGBioAwcOOPr48UTk8+fP69SpU3X24+3tLV9fX6cXAAAwl0uhxsvLS5GRkcrNzXUss9lsys3NVWxsbK3bxMbGOrWXpA0bNtTZXpKOHDmib775Rp07d3b0UVZWpvz8fEebDz74QDabTTExMa4cAgAAMJTLt3RbrVb913/9l1auXKkvvvhCU6dOVWVlpSZNmiRJSkpKUlpamqP9jBkzlJOTo+eff1779+/XwoULtXPnTqWmpkqSTp8+rV//+tfaunWrDh06pNzcXN19993q1auX4uPjJUn9+vXTmDFjNHnyZG3fvl2ffPKJUlNTNX78+Abd+QQAAMzn8pyacePG6cSJE5o/f75KSkoUERGhnJwcx2TgoqIieXh8n5WGDx+u7OxszZ07V08++aR69+6tdevWqX///pIkT09P7dmzRytXrlRZWZlCQ0N166236re//a28vb0d/bzxxhtKTU3V6NGj5eHhocTERL344ouXe/wAAMAQLj+npqXiOTUAAFxZLeo5NQAAAM0VoQYAABiBUAMAAIxAqAEAAEYg1AAAACMQagAAgBEINQAAwAiEGgAAYARCDQAAMAKhBgAAGIFQAwAAjECoAQAARiDUAAAAIxBqAACAEQg1AADACIQaAABgBEINAAAwAqEGAAAYgVADAACMQKgBAABGINQAAAAjEGoAAIARCDUAAMAIhBoAAGAEQg0AADACoQYAABiBUAMAAIxAqAEAAEYg1AAAACMQagAAgBEINQAAwAiEGgAAYARCDQAAMAKhBgAAGIFQAwAAjECoAQAARiDUAAAAIxBqAACAEQg1AADACIQaAABghEaFmiVLlig8PFw+Pj6KiYnR9u3b622/Zs0a9e3bVz4+PhowYIDeeecdx7pz585p9uzZGjBggNq1a6fQ0FAlJSXp2LFjTn2Eh4fLYrE4vRYtWtSY8gEAgIFcDjWrV6+W1WrVggULtGvXLg0aNEjx8fE6fvx4re23bNmiCRMmKCUlRbt371ZCQoISEhK0d+9eSdKZM2e0a9cuzZs3T7t27dKbb76pwsJC3XXXXRf19cwzz6i4uNjxmj59uqvlAwAAQ1nsdrvdlQ1iYmIUFRWll19+WZJks9kUFham6dOna86cORe1HzdunCorK7V+/XrHsmHDhikiIkJZWVm17mPHjh2Kjo7W4cOH1bVrV0kXRmpmzpypmTNnulKuQ0VFhfz8/FReXi5fX99G9VGfvNefaPI+AQBoSWJTMpq8T1c+v10aqamurlZ+fr7i4uK+78DDQ3FxccrLy6t1m7y8PKf2khQfH19ne0kqLy+XxWKRv7+/0/JFixYpICBAgwcP1nPPPafz58/X2UdVVZUqKiqcXgAAwFytXGl88uRJ1dTUKDg42Gl5cHCw9u/fX+s2JSUltbYvKSmptf3Zs2c1e/ZsTZgwwSmRPf744xoyZIg6duyoLVu2KC0tTcXFxVq8eHGt/aSnp+vpp5925fAAAEAL5lKoudLOnTun++67T3a7Xa+++qrTOqvV6vh54MCB8vLy0iOPPKL09HR5e3tf1FdaWprTNhUVFQoLC7tyxQMAALdyKdQEBgbK09NTpaWlTstLS0sVEhJS6zYhISENav9doDl8+LA++OCDS143i4mJ0fnz53Xo0CH16dPnovXe3t61hh0AAGAml+bUeHl5KTIyUrm5uY5lNptNubm5io2NrXWb2NhYp/aStGHDBqf23wWaL7/8Uhs3blRAQMAlaykoKJCHh4eCgoJcOQQAAGAoly8/Wa1WJScna+jQoYqOjlZmZqYqKys1adIkSVJSUpK6dOmi9PR0SdKMGTM0YsQIPf/887rjjju0atUq7dy5U0uXLpV0IdDce++92rVrl9avX6+amhrHfJuOHTvKy8tLeXl52rZtm0aNGqUOHTooLy9Ps2bN0v3336/rrruuqc4FAABowVwONePGjdOJEyc0f/58lZSUKCIiQjk5OY7JwEVFRfLw+H4AaPjw4crOztbcuXP15JNPqnfv3lq3bp369+8vSTp69Kj+/ve/S5IiIiKc9rVp0yaNHDlS3t7eWrVqlRYuXKiqqip1795ds2bNcpozAwAArm0uP6empeI5NQAAXFkt6jk1AAAAzRWhBgAAGIFQAwAAjECoAQAARiDUAAAAIxBqAACAEQg1AADACIQaAABgBEINAAAwAqEGAAAYgVADAACMQKgBAABGINQAAAAjEGoAAIARCDUAAMAIhBoAAGAEQg0AADACoQYAABiBUAMAAIxAqAEAAEYg1AAAACMQagAAgBEINQAAwAiEGgAAYARCDQAAMAKhBgAAGIFQAwAAjECoAQAARiDUAAAAIxBqAACAEQg1AADACIQaAABgBEINAAAwAqEGAAAYgVADAACMQKgBAABGINQAAAAjEGoAAIARCDUAAMAIhBoAAGCERoWaJUuWKDw8XD4+PoqJidH27dvrbb9mzRr17dtXPj4+GjBggN555x2n9Xa7XfPnz1fnzp3Vpk0bxcXF6csvv3Rqc+rUKU2cOFG+vr7y9/dXSkqKTp8+3ZjyAQCAgVwONatXr5bVatWCBQu0a9cuDRo0SPHx8Tp+/Hit7bds2aIJEyYoJSVFu3fvVkJCghISErR3715Hm2effVYvvviisrKytG3bNrVr107x8fE6e/aso83EiRO1b98+bdiwQevXr9dHH32kKVOmNOKQAQCAiSx2u93uygYxMTGKiorSyy+/LEmy2WwKCwvT9OnTNWfOnIvajxs3TpWVlVq/fr1j2bBhwxQREaGsrCzZ7XaFhobqV7/6lZ544glJUnl5uYKDg7VixQqNHz9eX3zxhW688Ubt2LFDQ4cOlSTl5OTo9ttv15EjRxQaGnrJuisqKuTn56fy8nL5+vq6csgNkvf6E03eJwAALUlsSkaT9+nK53crVzqurq5Wfn6+0tLSHMs8PDwUFxenvLy8WrfJy8uT1Wp1WhYfH69169ZJkr766iuVlJQoLi7Osd7Pz08xMTHKy8vT+PHjlZeXJ39/f0egkaS4uDh5eHho27Zt+sUvfnHRfquqqlRVVeV4X15eLunCybkSKv9ddelGAAAY7Ep8xn7XZ0PGYFwKNSdPnlRNTY2Cg4OdlgcHB2v//v21blNSUlJr+5KSEsf675bV1yYoKMi58Fat1LFjR0ebH0tPT9fTTz990fKwsLC6Dg8AAFyO6S9fsa6//fZb+fn51dvGpVDTkqSlpTmNENlsNp06dUoBAQGyWCxuqamiokJhYWH6+uuvr8glsJaO81M3zk39OD/14/zUj/NTP3efH7vdrm+//bZBU01cCjWBgYHy9PRUaWmp0/LS0lKFhITUuk1ISEi97b/7b2lpqTp37uzUJiIiwtHmxxORz58/r1OnTtW5X29vb3l7ezst8/f3r/8ArxJfX1/+4dSD81M3zk39OD/14/zUj/NTP3een0uN0HzHpbufvLy8FBkZqdzcXMcym82m3NxcxcbG1rpNbGysU3tJ2rBhg6N99+7dFRIS4tSmoqJC27Ztc7SJjY1VWVmZ8vPzHW0++OAD2Ww2xcTEuHIIAADAUC5ffrJarUpOTtbQoUMVHR2tzMxMVVZWatKkSZKkpKQkdenSRenp6ZKkGTNmaMSIEXr++ed1xx13aNWqVdq5c6eWLl0qSbJYLJo5c6Z+97vfqXfv3urevbvmzZun0NBQJSQkSJL69eunMWPGaPLkycrKytK5c+eUmpqq8ePHN2g4CgAAmM/lUDNu3DidOHFC8+fPV0lJiSIiIpSTk+OY6FtUVCQPj+8HgIYPH67s7GzNnTtXTz75pHr37q1169apf//+jja/+c1vVFlZqSlTpqisrEw//elPlZOTIx8fH0ebN954Q6mpqRo9erQ8PDyUmJioF1988XKO/arz9vbWggULLroshgs4P3Xj3NSP81M/zk/9OD/1a0nnx+Xn1AAAADRHfPcTAAAwAqEGAAAYgVADAACMQKgBAABGINS4yV133aWuXbvKx8dHnTt31gMPPKBjx465u6xm4dChQ0pJSVH37t3Vpk0b9ezZUwsWLFB1dbW7S2s2/vM//1PDhw9X27Ztm81DJd1pyZIlCg8Pl4+Pj2JiYrR9+3Z3l9QsfPTRR7rzzjsVGhoqi8Xi+M49XPgqnaioKHXo0EFBQUFKSEhQYWGhu8tqNl599VUNHDjQ8cC92NhYvfvuu+4u65IINW4yatQo/eUvf1FhYaH+9re/6eDBg7r33nvdXVazsH//ftlsNr322mvat2+fXnjhBWVlZenJJ590d2nNRnV1tcaOHaupU6e6uxS3W716taxWqxYsWKBdu3Zp0KBBio+Pv+gp5NeiyspKDRo0SEuWLHF3Kc3Ohx9+qGnTpmnr1q3asGGDzp07p1tvvVWVlZXuLq1ZuP7667Vo0SLl5+dr586duuWWW3T33Xdr37597i6tfnY0C2+99ZbdYrHYq6ur3V1Ks/Tss8/au3fv7u4ymp3ly5fb/fz83F2GW0VHR9unTZvmeF9TU2MPDQ21p6enu7Gq5keSfe3ate4uo9k6fvy4XZL9ww8/dHcpzdZ1111n/9Of/uTuMurFSE0zcOrUKb3xxhsaPny4Wrdu7e5ymqXy8nJ17NjR3WWgmamurlZ+fr7i4uIcyzw8PBQXF6e8vDw3VoaWpry8XJL4PVOLmpoarVq1SpWVlXV+JVJzQahxo9mzZ6tdu3YKCAhQUVGR3nrrLXeX1CwdOHBAL730kh555BF3l4Jm5uTJk6qpqXE80fw7wcHBKikpcVNVaGlsNptmzpypm266yelp99e6zz77TO3bt5e3t7ceffRRrV27VjfeeKO7y6oXoaYJzZkzRxaLpd7X/v37He1//etfa/fu3Xr//ffl6emppKQk2Q1+wLOr50eSjh49qjFjxmjs2LGaPHmymyq/OhpzfgBcvmnTpmnv3r1atWqVu0tpVvr06aOCggJt27ZNU6dOVXJysj7//HN3l1UvviahCZ04cULffPNNvW169OghLy+vi5YfOXJEYWFh2rJlS7Mf3mssV8/PsWPHNHLkSA0bNkwrVqxw+k4xEzXm78+KFSs0c+ZMlZWVXeHqmqfq6mq1bdtWf/3rXx1fgCtJycnJKisrY/TzBywWi9auXet0niClpqbqrbfe0kcffaTu3bu7u5xmLS4uTj179tRrr73m7lLq5PIXWqJunTp1UqdOnRq1rc1mkyRVVVU1ZUnNiivn5+jRoxo1apQiIyO1fPly4wONdHl/f65VXl5eioyMVG5uruPD2mazKTc3V6mpqe4tDs2a3W7X9OnTtXbtWm3evJlA0wA2m63Zf0YRatxg27Zt2rFjh37605/quuuu08GDBzVv3jz17NnT2FEaVxw9elQjR45Ut27dlJGRoRMnTjjWhYSEuLGy5qOoqEinTp1SUVGRampqVFBQIEnq1auX2rdv797irjKr1ark5GQNHTpU0dHRyszMVGVlpSZNmuTu0tzu9OnTOnDggOP9V199pYKCAnXs2FFdu3Z1Y2XuN23aNGVnZ+utt95Shw4dHHOw/Pz81KZNGzdX535paWm67bbb1LVrV3377bfKzs7W5s2b9d5777m7tPq59+ara9OePXvso0aNsnfs2NHu7e1tDw8Ptz/66KP2I0eOuLu0ZmH58uV2SbW+cEFycnKt52fTpk3uLs0tXnrpJXvXrl3tXl5e9ujoaPvWrVvdXVKzsGnTplr/niQnJ7u7NLer63fM8uXL3V1as/DQQw/Zu3XrZvfy8rJ36tTJPnr0aPv777/v7rIuiTk1AADACOZPVAAAANcEQg0AADACoQYAABiBUAMAAIxAqAEAAEYg1AAAACMQagAAgBEINQAAwAiEGgBNJjw8XJmZmXWuP3TokCwWi+NrHRrTBwDUhVAD4KoJCwtTcXGx+vfvf1X3++CDD161b6cuLi7WL3/5S91www3y8PDQzJkzr8p+ARBqAFxFnp6eCgkJUatW5n6XblVVlTp16qS5c+dq0KBB7i4HuKYQagA0yMiRI5WamqrU1FT5+fkpMDBQ8+bN04+/Pu7MmTN66KGH1KFDB3Xt2lVLly51rGvI5acfKysr08MPP6xOnTrJ19dXt9xyiz799FPH+oULFyoiIkKvvfaawsLC1LZtW913330qLy93rF+5cqXeeustWSwWWSwWbd68WZL02Wef6ZZbblGbNm0UEBCgKVOm6PTp046+vxvhycjIUOfOnRUQEKBp06bp3LlzddYbHh6uP/7xj0pKSpKfn1+DjxPA5SPUAGiwlStXqlWrVtq+fbv++Mc/avHixfrTn/7k1Ob555/X0KFDtXv3bj322GOaOnWqCgsLG73PsWPH6vjx43r33XeVn5+vIUOGaPTo0Tp16pSjzYEDB/SXv/xF//jHP5STk+PYtyQ98cQTuu+++zRmzBgVFxeruLhYw4cPV2VlpeLj43Xddddpx44dWrNmjTZu3KjU1FSn/W/atEkHDx7Upk2btHLlSq1YsUIrVqxo9PEAuILc/C3hAFqIESNG2Pv162e32WyOZbNnz7b369fP8b5bt272+++/3/HeZrPZg4KC7K+++qrdbrfbv/rqK7sk++7du+vcT7du3ewvvPCC3W632z/++GO7r6+v/ezZs05tevbsaX/ttdfsdrvdvmDBArunp6f9yJEjjvXvvvuu3cPDw15cXGy32+325ORk+9133+3Ux9KlS+3XXXed/fTp045lb7/9tt3Dw8NeUlLi2K5bt2728+fPO9qMHTvWPm7cuDrr/6ERI0bYZ8yY0aC2AC4fIzUAGmzYsGGyWCyO97Gxsfryyy9VU1PjWDZw4EDHzxaLRSEhITp+/Hij9vfpp5/q9OnTCggIUPv27R2vr776SgcPHnS069q1q7p06eJUl81mq3eE6IsvvtCgQYPUrl07x7Kbbrrpou1+8pOfyNPT0/G+c+fOjT4eAFeWubP1ALhF69atnd5bLBbZbLZG9XX69Gl17tzZMQfmh/z9/RvVp6ua8ngAXFmEGgANtm3bNqf3W7duVe/evZ1GMprSkCFDVFJSolatWik8PLzOdkVFRTp27JhCQ0MddXl4eKhPnz6SJC8vL6fRJEnq16+fVqxYocrKSsdozSeffOK0HYCWhctPABqsqKhIVqtVhYWF+vOf/6yXXnpJM2bMuGL7i4uLU2xsrBISEvT+++/r0KFD2rJli5566int3LnT0c7Hx0fJycn69NNP9fHHH+vxxx/Xfffdp5CQEEkX7kjas2ePCgsLdfLkSZ07d04TJ050bLd3715t2rRJ06dP1wMPPKDg4ODLqrugoEAFBQU6ffq0Tpw4oYKCAn3++eeX1SeAS2OkBkCDJSUl6d///reio6Pl6empGTNmaMqUKVdsfxaLRe+8846eeuopTZo0SSdOnFBISIhuvvlmp+DRq1cv3XPPPbr99tt16tQp/fznP9crr7ziWD958mRt3rxZQ4cO1enTp7Vp0yaNHDlS7733nmbMmKGoqCi1bdtWiYmJWrx48WXXPXjwYMfP+fn5ys7OVrdu3XTo0KHL7htA3Sx2+48eMgEAtRg5cqQiIiKa3VcYLFy4UOvWrXPp2TcAzMTlJwAAYARCDQAAMAKXnwAAgBEYqQEAAEYg1AAAACMQagAAgBEINQAAwAiEGgAAYARCDQAAMAKhBgAAGIFQAwAAjPD/A6/VO07MEVFsAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Calculate transverse momentum for WW and HWW truth\n", "p_T_ww = np.sqrt(ww_data[\"p_l_1_x_truth\"]**2 + ww_data[\"p_l_1_y_truth\"]**2)\n", "p_T_hww = np.sqrt(hww_data[\"p_l_1_x_truth\"]**2 + hww_data[\"p_l_1_y_truth\"]**2)\n", "\n", "# Compute eta\n", "p_norm_ww1 = np.sqrt(ww_data[\"p_l_1_x_truth\"]**2 + ww_data[\"p_l_1_y_truth\"]**2 + ww_data[\"p_l_1_z_truth\"]**2)\n", "eta_ww1 = 0.5 * np.log((p_norm_ww1 + ww_data[\"p_l_1_z_truth\"]) / (p_norm_ww1 - ww_data[\"p_l_1_z_truth\"] + 1e-15))\n", "p_norm_ww2 = np.sqrt(ww_data[\"p_l_2_x_truth\"]**2 + ww_data[\"p_l_2_y_truth\"]**2 + ww_data[\"p_l_2_z_truth\"]**2)\n", "eta_ww2 = 0.5 * np.log((p_norm_ww2 + ww_data[\"p_l_2_z_truth\"]) / (p_norm_ww2 - ww_data[\"p_l_2_z_truth\"] + 1e-15))\n", "\n", "delta_eta_ww = eta_ww1 - eta_ww2\n", "\n", "p_norm_hww1 = np.sqrt(hww_data[\"p_l_1_x_truth\"]**2 + hww_data[\"p_l_1_y_truth\"]**2 + hww_data[\"p_l_1_z_truth\"]**2)\n", "eta_hww1 = 0.5 * np.log((p_norm_hww1 + hww_data[\"p_l_1_z_truth\"]) / (p_norm_hww1 - hww_data[\"p_l_1_z_truth\"] + 1e-15))\n", "p_norm_hww2 = np.sqrt(hww_data[\"p_l_2_x_truth\"]**2 + hww_data[\"p_l_2_y_truth\"]**2 + hww_data[\"p_l_2_z_truth\"]**2)\n", "eta_hww2 = 0.5 * np.log((p_norm_hww2 + hww_data[\"p_l_2_z_truth\"]) / (p_norm_hww2 - hww_data[\"p_l_2_z_truth\"] + 1e-15))\n", "delta_eta_hww = eta_hww1 - eta_hww2\n", "\n", "# Plot delta eta comparison\n", "plt.hist(delta_eta_ww, bins=50, alpha=0.5, label=\"Sherpa\", range=(-3, 3), density=True)\n", "plt.hist(delta_eta_hww, bins=50, alpha=0.5, label=\"MG\", range=(-3, 3), density=True)\n", "plt.xlabel(\"delta eta\")\n", "plt.legend()\n", "plt.show()\n", "\n", "# Compute phi for WW truth and HWW truth\n", "phi_ww = np.arctan2(ww_data[\"p_l_1_y_truth\"], ww_data[\"p_l_1_x_truth\"])\n", "phi_hww = np.arctan2(hww_data[\"p_l_1_y_truth\"], hww_data[\"p_l_1_x_truth\"])\n", "\n", "delta_phi_ww = np.arctan2(ww_data[\"p_l_1_y_truth\"], ww_data[\"p_l_1_x_truth\"]) - np.arctan2(ww_data[\"p_l_2_y_truth\"], ww_data[\"p_l_2_x_truth\"])\n", "delta_phi_hww = np.arctan2(hww_data[\"p_l_1_y_truth\"], hww_data[\"p_l_1_x_truth\"]) - np.arctan2(hww_data[\"p_l_2_y_truth\"], hww_data[\"p_l_2_x_truth\"])\n", "delta_phi_hww = (delta_phi_hww + np.pi) % (2 * np.pi) - np.pi\n", "delta_phi_ww = (delta_phi_ww + np.pi) % (2 * np.pi) - np.pi\n", "\n", "# Plot delta phi comparison\n", "plt.hist(delta_phi_ww, bins=50, alpha=0.5, label=\"Sherpa\", range=(-2 * np.pi, 2 * np.pi), density=True)\n", "plt.hist(delta_phi_hww, bins=50, alpha=0.5, label=\"MG\", range=(-2 * np.pi, 2 * np.pi), density=True)\n", "plt.xlabel(\"delta phi\")\n", "plt.legend()\n", "plt.show()\n", "\n", "\n", "# Plot p_T comparison\n", "plt.hist(p_T_ww, bins=50, alpha=0.5, label=\"Sherpa\", range=(0, 150), density=True)\n", "plt.hist(p_T_hww, bins=50, alpha=0.5, label=\"MG\", range=(0, 150), density=True)\n", "plt.xlabel(\"p_T lepton 1\")\n", "plt.legend()\n", "plt.show()\n", "\n", "# Plot eta comparison\n", "plt.hist(eta_ww1, bins=50, alpha=0.5, label=\"Sherpa\", range=(-5, 5), density=True)\n", "plt.hist(eta_hww1, bins=50, alpha=0.5, label=\"MG\", range=(-5, 5), density=True)\n", "plt.xlabel(\"eta lepton 1\")\n", "plt.legend()\n", "plt.show()\n", "\n", "# Plot phi comparison\n", "plt.hist(phi_ww, bins=50, alpha=0.5, label=\"Sherpa\", range=(-np.pi, np.pi), density=True)\n", "plt.hist(phi_hww, bins=50, alpha=0.5, label=\"MG\", range=(-np.pi, np.pi), density=True)\n", "plt.xlabel(\"phi lepton 1\")\n", "plt.legend()\n", "plt.show()\n", "\n", "met_ww = np.sqrt(ww_data[\"p_v_1_x_truth\"]**2 + ww_data[\"p_v_1_y_truth\"]**2)\n", "met_hww = np.sqrt(hww_data[\"p_v_1_x_truth\"]**2 + hww_data[\"p_v_1_y_truth\"]**2)\n", "\n", "# Plot met comparison\n", "plt.hist(met_ww, bins=50, alpha=0.5, label=\"Sherpa\", range=(0, 150), density=True)\n", "plt.hist(met_hww, bins=50, alpha=0.5, label=\"MG\", range=(0, 150), density=True)\n", "plt.xlabel(\"MET\")\n", "plt.legend()\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}